# BPO CostCenter API 修正总结

## 修正的文件

### apps\web-antd\src\api\bpo\costCenter.ts

**修正前的问题：**

- 使用旧版本的vben格式
- 导入路径使用`@/`前缀
- 使用`defHttp`而不是`requestClient`
- 函数定义使用箭头函数而不是标准函数声明
- 缺少完整的JSDoc注释
- 类型定义混合在API文件中

**修正后的改进：**

#### 1. 导入更新

```typescript
// 修正前
import { defHttp } from '@/utils/http/axios';

// 修正后
import type { CostCenter, CostCenterContact } from './costCenter/model';
import type { ID, IDS, PageQuery } from '#/api/common';
import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';
```

#### 2. 类型定义分离

- 将接口定义移动到独立的`model.d.ts`文件
- 使用标准的vben5类型：`ID`, `IDS`, `PageQuery`
- 继承`BaseEntity`基础实体类

#### 3. API函数标准化

```typescript
// 修正前
export const getCostCenterList = (params?: CostCenterPageParams) =>
  defHttp.get<any>({
    url: Api.CostCenter + '/list',
    params,
  });

// 修正后
/**
 * 获取成本中心列表
 * @param params 参数
 * @returns CostCenter[]
 */
export function getCostCenterList(params?: PageQuery) {
  return requestClient.get<CostCenter[]>(Api.costCenterList, { params });
}
```

#### 4. 完整的API函数列表

**成本中心相关：**

- `getCostCenterList()` - 获取成本中心列表
- `exportCostCenter()` - 导出成本中心信息
- `getCostCenter()` - 查询成本中心详情
- `addCostCenter()` - 新增成本中心
- `updateCostCenter()` - 更新成本中心
- `delCostCenter()` - 删除成本中心
- `getCostCenterByProject()` - 根据项目ID查询成本中心

**联系人相关：**

- `getContactList()` - 查询联系人列表
- `getContactByCostCenter()` - 根据成本中心ID查询联系人
- `saveContactBatch()` - 批量保存联系人信息
- `exportContact()` - 导出联系人信息

#### 5. 错误处理和消息提示

- 使用`requestClient.postWithMsg()`、`putWithMsg()`、`deleteWithMsg()`自动显示操作结果消息
- 使用`commonExport()`统一处理导出功能

#### 6. API路径规范化

```typescript
enum Api {
  costCenterExport = '/bpo/costCenter/export',
  costCenterList = '/bpo/costCenter/list',
  costCenterContact = '/bpo/costCenterContact',
  root = '/bpo/costCenter',
}
```

## 相关文件结构

```
src/api/bpo/
├── costCenter.ts              # 主API文件（已修正）
└── costCenter/
    ├── index.ts              # 标准API导出文件
    └── model.d.ts           # TypeScript类型定义
```

## 使用示例

```typescript
// 在组件中使用
import {
  getCostCenterList,
  addCostCenter,
  delCostCenter,
} from '#/api/bpo/costCenter';

// 获取列表
const list = await getCostCenterList({ pageNum: 1, pageSize: 10 });

// 新增
await addCostCenter({ costCenterName: '测试中心', costCenterCode: 'TEST001' });

// 删除
await delCostCenter([1, 2, 3]);
```

## 主要改进点

1. **标准化**：符合vben5的API编写规范
2. **类型安全**：完整的TypeScript类型定义
3. **文档完善**：每个函数都有详细的JSDoc注释
4. **错误处理**：统一的错误处理和消息提示
5. **代码复用**：使用公共的导出和请求函数
6. **维护性**：清晰的文件结构和命名规范

## 注意事项

1. **后端接口**：确保后端API路径与前端配置一致
2. **权限控制**：API调用需要相应的权限码
3. **数据格式**：确保前后端数据格式一致
4. **错误处理**：使用统一的错误处理机制

## 测试建议

1. 测试所有CRUD操作
2. 测试分页和搜索功能
3. 测试导出功能
4. 测试联系人相关操作
5. 测试错误处理和消息提示

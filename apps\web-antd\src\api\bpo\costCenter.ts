import type { CostCenter, CostCenterContact } from './costCenter/model';

import type { ID, IDS, PageQuery } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  costCenterContact = '/bpo/costCenterContact',
  costCenterExport = '/bpo/costCenter/export',
  costCenterList = '/bpo/costCenter/list',
  root = '/bpo/costCenter',
}

/**
 * 获取成本中心列表
 * @param params 参数
 * @returns CostCenter[]
 */
export function getCostCenterList(params?: PageQuery) {
  return requestClient.get<CostCenter[]>(Api.costCenterList, { params });
}

/**
 * 导出成本中心信息
 * @param data 请求参数
 * @returns blob
 */
export function exportCostCenter(data: Partial<CostCenter>) {
  return commonExport(Api.costCenterExport, data);
}

/**
 * 查询成本中心信息
 * @param costCenterId id
 * @returns 成本中心信息
 */
export function getCostCenter(costCenterId: ID) {
  return requestClient.get<CostCenter>(`${Api.root}/${costCenterId}`);
}

/**
 * 成本中心新增
 * @param data 参数
 * @returns void
 */
export function addCostCenter(data: Partial<CostCenter>) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 成本中心更新
 * @param data 参数
 * @returns void
 */
export function updateCostCenter(data: Partial<CostCenter>) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 成本中心删除
 * @param costCenterIds ids
 * @returns void
 */
export function delCostCenter(costCenterIds: IDS) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${costCenterIds}`);
}

/**
 * 根据项目ID查询成本中心列表
 * @param projectId 项目ID
 * @returns CostCenter[]
 */
export function getCostCenterByProject(projectId: ID) {
  return requestClient.get<CostCenter[]>(
    `${Api.root}/listByProject/${projectId}`,
  );
}

/**
 * 查询联系人列表
 * @param params 参数
 * @returns CostCenterContact[]
 */
export function getContactList(params?: PageQuery) {
  return requestClient.get<CostCenterContact[]>(
    `${Api.costCenterContact}/list`,
    { params },
  );
}

/**
 * 根据成本中心ID查询联系人列表
 * @param costCenterId 成本中心ID
 * @returns CostCenterContact[]
 */
export function getContactByCostCenter(costCenterId: ID) {
  return requestClient.get<CostCenterContact[]>(
    `${Api.costCenterContact}/listByCostCenter/${costCenterId}`,
  );
}

/**
 * 批量保存联系人信息
 * @param costCenterId 成本中心ID
 * @param data 联系人数据
 * @returns void
 */
export function saveContactBatch(costCenterId: ID, data: CostCenterContact[]) {
  return requestClient.postWithMsg<void>(
    `${Api.costCenterContact}/saveBatch/${costCenterId}`,
    data,
  );
}

/**
 * 导出联系人信息
 * @param data 请求参数
 * @returns blob
 */
export function exportContact(data: Partial<CostCenterContact>) {
  return commonExport(`${Api.costCenterContact}/export`, data);
}

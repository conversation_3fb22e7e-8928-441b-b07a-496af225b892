import type { CostCenter } from './model';

import type { ID, IDS, PageQuery } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  costCenterExport = '/bpo/costCenter/export',
  costCenterList = '/bpo/costCenter/list',
  root = '/bpo/costCenter',
}

/**
 * 获取成本中心列表
 * @param params 参数
 * @returns CostCenter[]
 */
export function getCostCenterList(params?: PageQuery) {
  return requestClient.get<CostCenter[]>(Api.costCenterList, { params });
}

/**
 * 导出成本中心信息
 * @param data 请求参数
 * @returns blob
 */
export function exportCostCenter(data: Partial<CostCenter>) {
  return commonExport(Api.costCenterExport, data);
}

/**
 * 查询成本中心信息
 * @param costCenterId id
 * @returns 成本中心信息
 */
export function getCostCenter(costCenterId: ID) {
  return requestClient.get<CostCenter>(`${Api.root}/${costCenterId}`);
}

/**
 * 成本中心新增
 * @param data 参数
 * @returns void
 */
export function addCostCenter(data: Partial<CostCenter>) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 成本中心更新
 * @param data 参数
 * @returns void
 */
export function updateCostCenter(data: Partial<CostCenter>) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 成本中心删除
 * @param costCenterIds ids
 * @returns void
 */
export function delCostCenter(costCenterIds: IDS) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${costCenterIds}`);
}

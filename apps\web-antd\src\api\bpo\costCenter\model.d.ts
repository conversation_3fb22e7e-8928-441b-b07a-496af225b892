import type { BaseEntity } from '#/api/common';

export interface CostCenterContact {
  id?: number;
  costCenterId?: number;
  contactName?: string;
  contactGender?: string;
  contactPhone?: string;
  contactEmail?: string;
  contactPosition?: string;
  isPrimary?: string;
  status?: string;
  createTime?: string;
  updateTime?: string;
}

export interface CostCenter extends BaseEntity {
  id?: number;
  customerId?: number;
  customerName?: string;
  projectId?: number;
  projectName?: string;
  costCenterName?: string;
  costCenterCode?: string;
  departmentId?: number;
  departmentName?: string;
  contactList?: CostCenterContact[];
  remark?: string;
}

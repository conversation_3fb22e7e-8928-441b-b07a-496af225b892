import type { CustomerBo, CustomerImportParam, CustomerVo } from './model';

import type { ID, IDS, PageQuery, PageResult } from '#/api/common';

import { commonExport, ContentTypeEnum } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  customerExport = '/bpo/customer/export',
  customerImport = '/bpo/customer/importData',
  customerImportTemplate = '/bpo/customer/importTemplate',
  customerList = '/bpo/customer/list',
  root = '/bpo/customer',
}

/**
 * 获取客户信息列表
 * @param params 参数
 * @returns PageResult<CustomerVo>
 */
export function getCustomerList(params?: PageQuery) {
  console.log('调用客户列表API:', Api.customerList, params);
  return requestClient.get<PageResult<CustomerVo>>(Api.customerList, {
    params,
  });
}

/**
 * 导出客户信息
 * @param data 请求参数
 * @returns blob
 */
export function exportCustomer(data: Partial<CustomerVo>) {
  console.log('调用导出客户API:', Api.customerExport, data);
  return commonExport(Api.customerExport, data);
}

/**
 * 查询客户信息详情
 * @param customerId id
 * @returns 客户信息
 */
export function getCustomer(customerId: ID) {
  console.log('调用客户详情API:', `${Api.root}/${customerId}`);
  return requestClient.get<CustomerVo>(`${Api.root}/${customerId}`);
}

/**
 * 客户新增
 * @param data 客户业务对象
 * @returns void
 */
export function addCustomer(data: CustomerBo) {
  console.log('调用新增客户API:', Api.root, data);
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 客户更新
 * @param data 客户业务对象
 * @returns void
 */
export function updateCustomer(data: CustomerBo) {
  console.log('调用更新客户API:', Api.root, data);
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 客户删除
 * @param customerIds ids
 * @returns void
 */
export function delCustomer(customerIds: IDS) {
  console.log('调用删除客户API:', `${Api.root}/${customerIds}`);
  return requestClient.deleteWithMsg<void>(`${Api.root}/${customerIds}`);
}

/**
 * 从excel导入客户
 * @param data 导入参数
 * @returns 导入结果
 */
export function importCustomer(data: CustomerImportParam) {
  console.log('调用导入客户API:', Api.customerImport, data);
  return requestClient.post<{ code: number; msg: string }>(
    Api.customerImport,
    data,
    {
      headers: {
        'Content-Type': ContentTypeEnum.FORM_DATA,
      },
      isTransformResponse: false,
    },
  );
}

/**
 * 下载客户导入模板
 * @returns blob
 */
export function downloadCustomerImportTemplate() {
  console.log('调用下载客户导入模板API:', Api.customerImportTemplate);
  return requestClient.post<Blob>(
    Api.customerImportTemplate,
    {},
    {
      isTransformResponse: false,
      responseType: 'blob',
    },
  );
}

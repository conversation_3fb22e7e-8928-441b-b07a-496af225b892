import type { BaseEntity } from '#/api/common';

/**
 * 客户导入参数
 * @param updateSupport 是否覆盖数据
 * @param file excel文件
 */
export interface CustomerImportParam {
  updateSupport: boolean;
  file: Blob | File;
}

/**
 * 客户业务对象 - 对应后端BpoCustomerBo
 */
export interface CustomerBo {
  id?: number; // 客户ID（编辑时必填）
  customerName?: string; // 客户名称（必填，最大100字符）
  departmentId?: string; // 所属部门ID
  remark?: string; // 备注信息
}

/**
 * 客户视图对象 - 对应后端BpoCustomerVo
 */
export interface CustomerVo extends BaseEntity {
  id?: number; // 客户ID
  customerName?: string; // 客户名称
  departmentId?: string; // 所属部门ID
  departmentName?: string; // 部门名称（用于列表显示）
  deptName?: string; // 部门名称（兼容字段）
  remark?: string; // 备注信息
  createTime?: string; // 创建时间
  updateTime?: string; // 更新时间
}

/**
 * 客户表单对象 - 用于前端表单编辑
 */
export interface CustomerForm {
  id?: number; // 客户ID
  customerName?: string; // 客户名称
  deptId?: number; // 部门ID（用于TreeSelect单选）
  remark?: string; // 备注信息
}

/**
 * 统一的Customer类型，兼容所有场景
 */
export type Customer = CustomerForm & CustomerVo;

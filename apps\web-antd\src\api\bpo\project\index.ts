import type {
  Project,
  ProjectBo,
  ProjectImportParam,
  ProjectVo,
} from './model';

import type { ID, IDS, PageQuery, PageResult } from '#/api/common';

import { commonExport, ContentTypeEnum } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  projectExport = '/bpo/project/export',
  projectImport = '/bpo/project/importData',
  projectImportTemplate = '/bpo/project/importTemplate',
  projectList = '/bpo/project/list',
  root = '/bpo/project',
}

/**
 * 获取项目信息列表
 * @param params 参数
 * @returns PageResult<ProjectVo>
 */
export function getProjectList(params?: PageQuery) {
  console.log('调用项目列表API:', Api.projectList, params);
  return requestClient.get<PageResult<ProjectVo>>(Api.projectList, { params });
}

/**
 * 导出项目信息
 * @param data 请求参数
 * @returns blob
 */
export function exportProject(data: Partial<ProjectVo>) {
  console.log('调用导出项目API:', Api.projectExport, data);
  return commonExport(Api.projectExport, data);
}

/**
 * 查询项目信息详情
 * @param projectId id
 * @returns 项目信息
 */
export function getProject(projectId: ID) {
  console.log('调用项目详情API:', `${Api.root}/${projectId}`);
  return requestClient.get<ProjectVo>(`${Api.root}/${projectId}`);
}

/**
 * 项目新增
 * @param data 项目业务对象
 * @returns void
 */
export function addProject(data: ProjectBo) {
  console.log('调用新增项目API:', Api.root, data);
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 项目更新
 * @param data 项目业务对象
 * @returns void
 */
export function updateProject(data: ProjectBo) {
  console.log('调用更新项目API:', Api.root, data);
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 项目删除
 * @param projectIds ids
 * @returns void
 */
export function delProject(projectIds: IDS) {
  console.log('调用删除项目API:', `${Api.root}/${projectIds}`);
  return requestClient.deleteWithMsg<void>(`${Api.root}/${projectIds}`);
}

/**
 * 从excel导入项目
 * @param data 导入参数
 * @returns 导入结果
 */
export function importProject(data: ProjectImportParam) {
  console.log('调用导入项目API:', Api.projectImport, data);
  return requestClient.post<{ code: number; msg: string }>(
    Api.projectImport,
    data,
    {
      headers: {
        'Content-Type': ContentTypeEnum.FORM_DATA,
      },
      isTransformResponse: false,
    },
  );
}

/**
 * 下载项目导入模板
 * @returns blob
 */
export function downloadProjectImportTemplate() {
  console.log('调用下载项目导入模板API:', Api.projectImportTemplate);
  return requestClient.post<Blob>(
    Api.projectImportTemplate,
    {},
    {
      isTransformResponse: false,
      responseType: 'blob',
    },
  );
}

/**
 * 根据客户ID查询项目列表
 * @param customerId 客户ID
 * @returns Project[]
 */
export function getProjectByCustomer(customerId: ID) {
  console.log(
    '调用根据客户查询项目API:',
    `${Api.root}/listByCustomer/${customerId}`,
  );
  return requestClient.get<Project[]>(
    `${Api.root}/listByCustomer/${customerId}`,
  );
}

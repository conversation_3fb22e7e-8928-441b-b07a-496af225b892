import type { BaseEntity } from '#/api/common';

/**
 * 项目导入参数
 * @param updateSupport 是否覆盖数据
 * @param file excel文件
 */
export interface ProjectImportParam {
  updateSupport: boolean;
  file: Blob | File;
}

/**
 * 项目业务对象 - 基于BpoProject.java，包含项目运营、入职、离职信息
 */
export interface ProjectBo {
  id?: number; // 项目ID（编辑时必填）

  // 项目运营信息
  projectName?: string; // 项目名称（必填，最大100字符）
  projectCode?: string; // 项目编码
  projectDescription?: string; // 项目描述
  projectStatus?: string; // 项目状态
  projectType?: string; // 项目类型
  projectStaffCount?: number; // 项目在职人数
  projectBudget?: number; // 项目预算
  projectStartDate?: string; // 项目开始日期
  projectEndDate?: string; // 项目结束日期
  businessType?: string; // 业务类型
  employeeRelation?: string; // 员工关系
  customerId?: string; // 客户ID
  projectManagerId?: string; // 项目经理ID
  settlementSpecialistId?: string; // 结算专员ID
  departmentId?: string; // 所属部门ID
  contractAmount?: number; // 合同金额
  contractStartDate?: string; // 合同开始日期
  contractEndDate?: string; // 合同结束日期
  billingCycle?: string; // 计费周期
  paymentMethod?: string; // 付款方式

  // 项目运营信息字段（根据项目信息.md）
  customerContact?: string; // 客户对接人
  customerContactPhone?: string; // 对接人联系方式
  recruitmentRequirement?: string; // 招聘需求
  requireMedicalExam?: string; // 是否要体检
  requireBackgroundCheck?: string; // 是否要背调
  probationPeriodTime?: string; // 试用期时间
  thirteenthSalaryTime?: string; // 13薪发放时间
  yearEndBonusRule?: string; // 年终奖发放规则
  yearEndBonusTime?: string; // 年终奖发放时间
  holidayBenefit?: string; // 端午中秋等节假日福利
  highTemperatureAllowance?: string; // 高温补贴
  vacationRule?: string; // 假期规则
  sickLeaveRule?: string; // 病假规则
  annualLeaveRule?: string; // 年假规则
  billingDate?: string; // 账单日
  billConfirmProcess?: string; // 账单确认流程
  payrollDate?: string; // 发薪日
  payrollProcess?: string; // 发薪流程
  paymentTerm?: string; // 账期
  invoiceProcess?: string; // 开票流程
  settlementProcess?: string; // 结算流程
  systemAccessLink?: string; // 仝力系统访问链接

  // 入职相关信息（根据项目信息.md）
  requireCriminalRecord?: string; // 是否需要无犯罪记录证明
  requireMedicalReport?: string; // 是否需体检报告
  requireResignationCert?: string; // 是否需要上家公司离职证明
  requireElectronicPhoto?: string; // 是否需电子一寸照
  requireProfessionalCert?: string; // 是否需职业证书
  currentAddressRequired?: string; // 现住地址是否必填
  requireEducationCert?: string; // 是否需要学历证书
  requireDegreeCert?: string; // 是否需要学位证书
  requireHouseholdRegister?: string; // 是否需要户口本
  requireSunshineDeclaration?: string; // 是否需要阳光申报
  ethnicityRequired?: string; // 民族是否必填
  maritalStatusRequired?: string; // 婚姻状况是否必填
  politicalStatusRequired?: string; // 政治面貌是否必填
  nativePlaceRequired?: string; // 籍贯是否必填
  householdTypeRequired?: string; // 户籍性质是否必填
  heightRequired?: string; // 身高是否必填
  weightRequired?: string; // 体重是否必填
  requireEmployeeRegistrationForm?: string; // 是否需要员工应聘登记表
  requireBankCardPhoto?: string; // 是否需要银行卡照片

  // 离职相关信息（根据项目信息.md）
  requireResignationForm?: string; // 是否需上传离职手续表
  requireHandoverForm?: string; // 是否需离职交接单
  requireResignationTemplate?: string; // 是否需要离职申请模版
  resignationTemplate?: string; // 离职申请模版

  remark?: string; // 备注信息
}

/**
 * 项目视图对象 - 基于BpoProject.java，包含项目运营、入职、离职信息
 */
export interface ProjectVo extends BaseEntity {
  id?: number; // 项目ID

  // 项目运营信息
  projectName?: string; // 项目名称
  projectCode?: string; // 项目编码
  projectDescription?: string; // 项目描述
  projectStatus?: string; // 项目状态
  projectStatusName?: string; // 项目状态名称（用于列表显示）
  projectType?: string; // 项目类型
  projectTypeName?: string; // 项目类型名称（用于列表显示）
  projectStaffCount?: number; // 项目在职人数
  projectBudget?: number; // 项目预算
  projectStartDate?: string; // 项目开始日期
  projectEndDate?: string; // 项目结束日期
  businessType?: string; // 业务类型
  businessTypeName?: string; // 业务类型名称（用于列表显示）
  employeeRelation?: string; // 员工关系
  employeeRelationName?: string; // 员工关系名称（用于列表显示）
  customerId?: string; // 客户ID
  customerName?: string; // 客户名称（用于列表显示）
  projectManagerId?: string; // 项目经理ID
  projectManagerName?: string; // 项目经理姓名（用于列表显示）
  settlementSpecialistId?: string; // 结算专员ID
  settlementSpecialistName?: string; // 结算专员姓名（用于列表显示）
  departmentId?: string; // 所属部门ID
  departmentName?: string; // 部门名称（用于列表显示）
  contractAmount?: number; // 合同金额
  contractStartDate?: string; // 合同开始日期
  contractEndDate?: string; // 合同结束日期
  billingCycle?: string; // 计费周期
  billingCycleName?: string; // 计费周期名称（用于列表显示）
  paymentMethod?: string; // 付款方式
  paymentMethodName?: string; // 付款方式名称（用于列表显示）

  // 新增字段（根据参考布局）
  customerContact?: string; // 客户对接人
  customerContactPhone?: string; // 对接人联系方式
  recruitmentRequirement?: string; // 招聘需求
  serviceFeeSettlement?: string; // 服务费结算方式
  serviceFeeCycle?: string; // 服务费结算周期
  companySettlement?: string; // 公司名称结算方式
  settlementStartDate?: string; // 结算周期开始时间
  settlementEndDate?: string; // 结算周期结束时间

  // 入职相关信息
  onboardingProcess?: string; // 入职流程状态
  onboardingDate?: string; // 入职日期
  probationPeriod?: number; // 试用期（月）
  probationEndDate?: string; // 试用期结束日期
  onboardingDocuments?: string; // 入职文件状态
  trainingRequired?: string; // 培训要求
  trainingStatus?: string; // 培训状态
  workLocation?: string; // 工作地点
  workSchedule?: string; // 工作时间安排
  emergencyContact?: string; // 紧急联系人
  emergencyPhone?: string; // 紧急联系电话

  // 离职相关信息
  resignationProcess?: string; // 离职流程状态
  resignationDate?: string; // 离职日期
  resignationReason?: string; // 离职原因
  resignationType?: string; // 离职类型
  handoverStatus?: string; // 交接状态
  handoverPerson?: string; // 交接人
  finalWorkDate?: string; // 最后工作日
  exitInterview?: string; // 离职面谈状态
  clearanceStatus?: string; // 离职清算状态
  rehireEligible?: string; // 是否可重新雇佣

  remark?: string; // 备注信息
  createBy?: string; // 创建者
  createTime?: string; // 创建时间
  updateBy?: string; // 更新者
  updateTime?: string; // 更新时间
}

/**
 * 项目实体 - 用于类型兼容
 */
export interface Project extends ProjectVo {}

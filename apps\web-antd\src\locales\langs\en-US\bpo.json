{"moduleName": "BPO Management", "employee": "Employee Management", "customer": "Customer Management", "project": "Project Management", "costCenter": "Cost Center Management", "costCenterContact": "Cost Center Contact", "costCenterForm": {"basicInfo": "Basic Information", "contactInfo": "Contact Information", "customer": "Customer", "project": "Project", "costCenterName": "Cost Center Name", "costCenterCode": "Cost Center Code", "department": "Department", "remark": "Remark", "addContact": "Add Contact", "contactName": "Contact Name", "contactGender": "Gender", "contactPhone": "Phone", "contactEmail": "Email", "contactPosition": "Position", "isPrimary": "Primary Contact", "status": "Status", "action": "Action", "delete": "Delete", "male": "Male", "female": "Female", "yes": "Yes", "no": "No", "normal": "Normal", "disabled": "Disabled"}, "table": {"customerId": "Customer ID", "customerName": "Customer Name", "departmentIds": "Department", "costCenterName": "Cost Center Name", "costCenterCode": "Cost Center Code", "projectName": "Project Name", "departmentName": "Department", "createTime": "Create Time", "updateTime": "Update Time", "remark": "Remark"}, "button": {"add": "Add", "edit": "Edit", "delete": "Delete", "export": "Export", "search": "Search", "reset": "Reset", "save": "Save", "cancel": "Cancel"}, "message": {"addSuccess": "Added successfully", "editSuccess": "Updated successfully", "deleteSuccess": "Deleted successfully", "exportSuccess": "Exported successfully", "saveSuccess": "Saved successfully", "deleteConfirm": "Are you sure to delete?", "atLeastOneContact": "Please add at least one contact", "setPrimaryContact": "Please set a primary contact", "onlyOnePrimaryContact": "Only one primary contact is allowed", "contactNameRequired": "Contact name cannot be empty"}, "placeholder": {"pleaseInput": "Please input", "pleaseSelect": "Please select", "inputCustomerName": "Please input customer name", "inputCostCenterName": "Please input cost center name", "inputCostCenterCode": "Please input cost center code", "selectCustomer": "Please select customer", "selectProject": "Please select project", "selectDepartment": "Please select department", "inputRemark": "Please input remark", "inputContactName": "Please input contact name", "selectGender": "Please select gender", "inputPhone": "Please input phone", "inputEmail": "Please input email", "inputPosition": "Please input position", "selectStatus": "Please select status"}, "validation": {"required": "is required", "customerRequired": "Please select customer", "projectRequired": "Please select project", "costCenterNameRequired": "Please input cost center name", "costCenterCodeRequired": "Please input cost center code", "maxLength": "Length cannot exceed {max} characters", "costCenterNameMaxLength": "Cost center name length cannot exceed 100 characters", "costCenterCodeMaxLength": "Cost center code length cannot exceed 50 characters", "remarkMaxLength": "Remark length cannot exceed 500 characters", "contactNameMaxLength": "Contact name length cannot exceed 50 characters", "phoneMaxLength": "Phone length cannot exceed 20 characters", "emailMaxLength": "Email length cannot exceed 100 characters", "positionMaxLength": "Position length cannot exceed 50 characters"}, "title": {"customerList": "Customer List", "addCustomer": "Add Customer", "editCustomer": "Edit Customer", "costCenterList": "Cost Center List", "addCostCenter": "Add Cost Center", "editCostCenter": "Edit Cost Center", "costCenterContactList": "Cost Center Contact List"}}
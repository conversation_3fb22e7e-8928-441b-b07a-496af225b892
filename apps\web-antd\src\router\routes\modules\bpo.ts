import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'ion:business-outline',
      order: 2000,
      title: $t('routes.bpo.moduleName'),
    },
    name: 'B<PERSON>',
    path: '/bpo',
    children: [
      {
        name: 'B<PERSON><PERSON>ust<PERSON>',
        path: '/bpo/customer',
        component: () => import('#/views/bpo/customer/index.vue'),
        meta: {
          title: $t('routes.bpo.customer'),
          icon: 'ant-design:team-outlined',
        },
      },
      {
        name: 'BpoCostCenter',
        path: '/bpo/cost-center',
        component: () => import('#/views/bpo/costCenter/index.vue'),
        meta: {
          title: $t('routes.bpo.costCenter'),
          icon: 'ant-design:gold-outlined',
        },
      },
      {
        name: 'BpoCostCenterContact',
        path: '/bpo/cost-center-contact',
        component: () => import('#/views/bpo/costCenter/contact/index.vue'),
        meta: {
          title: $t('routes.bpo.costCenterContact'),
          icon: 'ant-design:contacts-outlined',
        },
      },
    ],
  },
];

export default routes;

<script setup lang="ts">
import type { CostCenter, CostCenterContact } from '#/api/bpo/costCenter/model';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  addCostCenter,
  getCostCenter,
  updateCostCenter,
} from '#/api/bpo/costCenter';
import { $t } from '#/locales';

import { contactColumns, drawerSchema } from './drawer-data';

defineOptions({ name: 'CostCenterModal' });

const emit = defineEmits(['reload']);

const isUpdate = ref(false);
const loading = ref(false);
const rowId = ref<number>();
const contactDataSource = ref<CostCenterContact[]>([]);
let tempIdCounter = 0;

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  schema: drawerSchema(),
  showDefaultActions: false,
});

const [DrawerComponent, drawerApi] = useVbenDrawer({
  onOpenChange: async (isOpen: boolean) => {
    if (!isOpen) return;

    const data = drawerApi.getData();
    isUpdate.value = !!data?.id;

    if (isUpdate.value && data?.id) {
      loading.value = true;
      try {
        const costCenter = await getCostCenter(data.id);
        await formApi.setValues(costCenter);
        // 设置联系人数据，添加临时ID用于表格行key
        contactDataSource.value = (costCenter.contactList || []).map(
          (item: any) => ({
            ...item,
            tempId: ++tempIdCounter,
          }),
        );
      } finally {
        loading.value = false;
      }
    } else {
      rowId.value = undefined;
      contactDataSource.value = [];
      tempIdCounter = 0;
      await formApi.resetForm();
    }
  },
});

const getTitle = computed(() =>
  isUpdate.value
    ? $t('bpo.title.editCostCenter')
    : $t('bpo.title.addCostCenter'),
);

async function handleSubmit() {
  try {
    const values = await formApi.validate();
    loading.value = true;

    // 验证联系人信息
    if (contactDataSource.value.length === 0) {
      message.warning($t('bpo.message.atLeastOneContact'));
      return;
    }

    // 检查是否有主要联系人
    const primaryContacts = contactDataSource.value.filter(
      (item: any) => item.isPrimary === 'Y',
    );
    if (primaryContacts.length === 0) {
      message.warning($t('bpo.message.setPrimaryContact'));
      return;
    }
    if (primaryContacts.length > 1) {
      message.warning($t('bpo.message.onlyOnePrimaryContact'));
      return;
    }

    // 验证联系人必填字段
    for (let i = 0; i < contactDataSource.value.length; i++) {
      const contact = contactDataSource.value[i];
      if (!contact?.contactName?.trim()) {
        message.warning($t('bpo.message.contactNameRequired'));
        return;
      }
    }

    const costCenter: CostCenter = {
      ...values,
      contactList: contactDataSource.value.map((item: any) => {
        const { tempId, ...rest } = item;
        return rest;
      }),
    };

    if (isUpdate.value) {
      costCenter.id = rowId.value;
      await updateCostCenter(costCenter);
    } else {
      await addCostCenter(costCenter);
    }

    drawerApi.close();
    emit('reload');
    message.success($t('bpo.message.saveSuccess'));
  } finally {
    loading.value = false;
  }
}

function handleCancel() {
  drawerApi.close();
}

function handleAddContact() {
  const newContact: CostCenterContact & { tempId: number } = {
    tempId: ++tempIdCounter,
    contactName: '',
    contactGender: '',
    contactPhone: '',
    contactEmail: '',
    contactPosition: '',
    isPrimary: 'N',
    status: '0',
  };
  contactDataSource.value.push(newContact);
}

function handleDeleteContact(index: number) {
  contactDataSource.value.splice(index, 1);
}

function handlePrimaryChange(record: CostCenterContact, index: number) {
  if (record.isPrimary === 'Y') {
    // 将其他联系人的主要联系人标识设为N
    contactDataSource.value.forEach((item: any, idx: number) => {
      if (idx !== index) {
        item.isPrimary = 'N';
      }
    });
  }
}
</script>

<template>
  <DrawerComponent
    :title="getTitle"
    :loading="loading"
    @confirm="handleSubmit"
    @cancel="handleCancel"
    width="1200px"
  >
    <div class="p-4">
      <!-- 成本中心基本信息 -->
      <div class="mb-6">
        <h3 class="mb-4 text-lg font-medium">
          {{ $t('bpo.costCenterForm.basicInfo') }}
        </h3>
        <BasicForm />
      </div>

      <!-- 联系人信息表格 -->
      <div>
        <div class="mb-4 flex items-center justify-between">
          <h3 class="text-lg font-medium">
            {{ $t('bpo.costCenterForm.contactInfo') }}
          </h3>
          <a-button type="primary" size="small" @click="handleAddContact">
            <PlusOutlined class="mr-1" />
            {{ $t('bpo.costCenterForm.addContact') }}
          </a-button>
        </div>

        <a-table
          :columns="contactColumns"
          :data-source="contactDataSource"
          :pagination="false"
          bordered
          size="small"
          row-key="tempId"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'contactName'">
              <a-input
                v-model:value="record.contactName"
                :placeholder="$t('bpo.placeholder.inputContactName')"
                size="small"
                :maxlength="50"
              />
            </template>
            <template v-else-if="column.key === 'contactGender'">
              <a-select
                v-model:value="record.contactGender"
                :placeholder="$t('bpo.placeholder.selectGender')"
                size="small"
                style="width: 100%"
              >
                <a-select-option value="男">
                  {{ $t('bpo.costCenterForm.male') }}
                </a-select-option>
                <a-select-option value="女">
                  {{ $t('bpo.costCenterForm.female') }}
                </a-select-option>
              </a-select>
            </template>
            <template v-else-if="column.key === 'contactPhone'">
              <a-input
                v-model:value="record.contactPhone"
                :placeholder="$t('bpo.placeholder.inputPhone')"
                size="small"
                :maxlength="20"
              />
            </template>
            <template v-else-if="column.key === 'contactEmail'">
              <a-input
                v-model:value="record.contactEmail"
                :placeholder="$t('bpo.placeholder.inputEmail')"
                size="small"
                :maxlength="100"
              />
            </template>
            <template v-else-if="column.key === 'contactPosition'">
              <a-input
                v-model:value="record.contactPosition"
                :placeholder="$t('bpo.placeholder.inputPosition')"
                size="small"
                :maxlength="50"
              />
            </template>
            <template v-else-if="column.key === 'isPrimary'">
              <a-radio-group
                v-model:value="record.isPrimary"
                size="small"
                @change="handlePrimaryChange(record, index)"
              >
                <a-radio value="Y">{{ $t('bpo.costCenterForm.yes') }}</a-radio>
                <a-radio value="N">{{ $t('bpo.costCenterForm.no') }}</a-radio>
              </a-radio-group>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-select
                v-model:value="record.status"
                :placeholder="$t('bpo.placeholder.selectStatus')"
                size="small"
                style="width: 100%"
              >
                <a-select-option value="0">
                  {{ $t('bpo.costCenterForm.normal') }}
                </a-select-option>
                <a-select-option value="1">
                  {{ $t('bpo.costCenterForm.disabled') }}
                </a-select-option>
              </a-select>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-button
                type="link"
                size="small"
                danger
                @click="handleDeleteContact(index)"
              >
                {{ $t('bpo.costCenterForm.delete') }}
              </a-button>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </DrawerComponent>
</template>

<style scoped>
.ant-table-tbody > tr > td {
  padding: 8px !important;
}
</style>

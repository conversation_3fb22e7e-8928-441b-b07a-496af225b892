# BPO CostCenter 目录修正总结

## 修正的文件

### 1. index.vue (主列表页面)

**修正内容：**

- 从旧版本vben格式更新为vben5标准格式
- 更新导入路径：`@/` → `#/`
- 更新组件导入：`BasicTable, useTable` → `useVbenVxeGrid`
- 更新模板结构：使用`Page`组件和`BasicTable`
- 更新表格配置：使用`VxeGridProps`格式
- 更新按钮和操作：使用vben5的标准按钮组件

**主要变更：**

```typescript
// 旧版本
import { BasicTable, useTable } from '@/components/Table';

// 新版本
import { useVbenVxeGrid } from '#/adapter/vxe-table';
```

### 2. CostCenterModal.vue (编辑弹窗)

**修正内容：**

- 从`BasicModal`更新为`VbenDrawer`
- 更新表单组件：`BasicForm` → `VbenForm`
- 更新导入路径和组件引用
- 添加国际化支持：使用`$t()`函数
- 更新事件处理和数据绑定方式

**主要变更：**

```vue
<!-- 旧版本 -->
<BasicModal @register="registerModal">
  <BasicForm @register="registerForm" />
</BasicModal>

<!-- 新版本 -->
<VbenDrawer :title="getTitle" @confirm="handleSubmit">
  <VbenForm ref="formRef" :schema="drawerSchema()" />
</VbenDrawer>
```

### 3. contact/index.vue (联系人列表页面)

**修正内容：**

- 更新为vben5标准格式
- 简化模板结构
- 更新表格配置和数据处理
- 添加Tag组件的渲染逻辑

### 4. 新增的数据配置文件

#### data.ts (主列表数据配置)

- 定义查询表单schema
- 定义表格列配置
- 符合vben5的`FormSchemaGetter`和`VxeGridProps`格式

#### drawer-data.ts (编辑弹窗数据配置)

- 定义编辑表单schema
- 定义联系人表格列配置
- 支持表单验证和组件配置

#### contact/data.ts (联系人列表数据配置)

- 定义联系人查询表单schema
- 定义联系人表格列配置
- 包含Tag组件的渲染逻辑

### 5. API文件结构调整

#### #/api/bpo/costCenter/index.ts

- 标准的API接口定义
- 使用vben5的`requestClient`
- 支持分页、导出等功能

#### #/api/bpo/costCenter/model.d.ts

- TypeScript类型定义
- 包含`CostCenter`和`CostCenterContact`接口

## 主要技术更新

### 1. 导入路径标准化

```typescript
// 旧版本
import { xxx } from '@/components/xxx';
import { xxx } from '@/api/xxx';

// 新版本
import { xxx } from '#/adapter/xxx';
import { xxx } from '#/api/xxx';
```

### 2. 表格组件更新

```typescript
// 旧版本
const [registerTable, { reload }] = useTable({...});

// 新版本
const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
```

### 3. 弹窗组件更新

```typescript
// 旧版本
const [registerModal, { openModal }] = useModal();

// 新版本
const [VbenDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: xxx,
});
```

### 4. 国际化支持

```typescript
// 添加国际化
import { $t } from '#/locales';

// 使用方式
title: $t('bpo.costCenter');
```

## 注意事项

1. **TypeScript错误**：部分导入可能显示错误，但这通常是IDE缓存问题，重启TypeScript服务可解决
2. **API接口**：需要确保后端API接口路径与前端配置一致
3. **权限控制**：使用`v-access:code`指令进行权限控制
4. **国际化**：所有文本都应使用国际化函数`$t()`

## 测试建议

1. 测试列表页面的查询、分页功能
2. 测试新增、编辑、删除功能
3. 测试联系人的添加、编辑、删除功能
4. 测试导出功能
5. 测试权限控制是否正常
6. 测试国际化切换是否正常

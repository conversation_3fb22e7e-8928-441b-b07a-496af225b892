# 成本中心管理前端代码说明

## 概述

基于vben5框架生成的成本中心管理前端代码，支持成本中心的CRUD操作，并在编辑界面中以表格形式直接录入和管理联系人信息。

## 文件结构

```
src/views/bpo/costCenter/
├── index.vue                    # 成本中心列表页面
├── CostCenterModal.vue          # 成本中心编辑模态框（包含联系人表格）
├── costCenter.data.ts           # 表单配置和表格列定义
├── contact/
│   └── index.vue               # 联系人独立管理页面（可选）
└── README.md                   # 说明文档

src/api/bpo/
└── costCenter.ts               # API接口定义

src/router/routes/modules/
└── bpo.ts                      # 路由配置

src/locales/lang/
├── zh-CN/routes/bpo.ts         # 中文国际化
└── en/routes/bpo.ts            # 英文国际化
```

## 核心功能

### 1. 成本中心管理

- **列表展示**：支持分页、搜索、排序
- **新增/编辑**：模态框形式，包含完整的表单验证
- **删除**：支持单个和批量删除，带确认提示
- **导出**：支持Excel导出功能

### 2. 联系人信息管理

- **内嵌表格**：在成本中心编辑界面中直接管理联系人
- **动态添加**：支持动态添加和删除联系人记录
- **表格编辑**：所有字段支持内联编辑
- **业务规则**：
  - 每个成本中心至少需要一个联系人
  - 只能设置一个主要联系人
  - 主要联系人切换时自动更新其他记录

### 3. 表单验证

- **必填验证**：成本中心名称、编号、项目等必填字段
- **长度验证**：各字段长度限制
- **业务验证**：联系人数量和主要联系人唯一性验证
- **关联验证**：客户和项目的级联选择

## 技术特点

### 1. 基于vben5框架

- 使用 `BasicTable` 组件实现表格功能
- 使用 `BasicForm` 组件实现表单功能
- 使用 `BasicModal` 组件实现模态框功能
- 支持权限控制和国际化

### 2. TypeScript支持

- 完整的类型定义
- API接口类型安全
- 组件属性类型检查

### 3. 响应式设计

- 支持不同屏幕尺寸
- 表格列宽自适应
- 移动端友好

### 4. 用户体验优化

- 加载状态提示
- 操作成功/失败消息提示
- 表单验证错误提示
- 确认操作对话框

## 使用方法

### 1. 安装依赖

确保项目已安装vben5相关依赖：

```bash
npm install
```

### 2. 配置路由

在 `src/router/routes/modules/bpo.ts` 中已配置相关路由。

### 3. 配置权限

确保后端已配置相应的权限码：

- `bpo:costCenter:list` - 查询权限
- `bpo:costCenter:add` - 新增权限
- `bpo:costCenter:edit` - 修改权限
- `bpo:costCenter:remove` - 删除权限
- `bpo:costCenter:export` - 导出权限

### 4. 配置菜单

在系统菜单管理中添加对应的菜单项。

## API接口

### 成本中心接口

- `GET /bpo/costCenter/list` - 查询列表
- `GET /bpo/costCenter/{id}` - 查询详情
- `POST /bpo/costCenter` - 新增
- `PUT /bpo/costCenter` - 修改
- `DELETE /bpo/costCenter/{ids}` - 删除
- `POST /bpo/costCenter/export` - 导出

### 联系人接口

- `GET /bpo/costCenterContact/list` - 查询列表
- `GET /bpo/costCenterContact/listByCostCenter/{id}` - 根据成本中心查询
- `POST /bpo/costCenterContact/saveBatch/{id}` - 批量保存

## 自定义配置

### 1. 表格列配置

在 `costCenter.data.ts` 中修改 `columns` 数组来调整表格列。

### 2. 表单字段配置

在 `costCenter.data.ts` 中修改 `costCenterFormSchema` 数组来调整表单字段。

### 3. 搜索条件配置

在 `costCenter.data.ts` 中修改 `searchFormSchema` 数组来调整搜索条件。

### 4. 联系人表格配置

在 `CostCenterModal.vue` 中的 `contactColumns` 配置中调整联系人表格。

## 扩展建议

### 1. 批量操作

可以添加批量编辑、批量导入等功能。

### 2. 高级搜索

可以添加更多搜索条件和高级搜索功能。

### 3. 数据可视化

可以添加成本中心的统计图表和数据分析功能。

### 4. 移动端优化

可以进一步优化移动端的显示效果。

## 注意事项

1. **数据同步**：联系人数据与成本中心数据保持同步
2. **权限控制**：确保用户有相应的操作权限
3. **数据验证**：前后端都需要进行数据验证
4. **错误处理**：合理处理API调用失败的情况
5. **性能优化**：大数据量时考虑分页和虚拟滚动

## 依赖的其他API

需要确保以下API接口可用：

- 客户列表API：`/bpo/customer/list`
- 项目列表API：`/bpo/project/list`
- 部门树API：`/system/dept/list`

这些API需要在对应的模块中实现。

# VbenForm 导入错误修正总结

## 问题描述

```
SyntaxError: The requested module '/src/adapter/form.ts' does not provide an export named 'VbenForm' (at CostCenterModal.vue:119:10)
```

## 问题原因

在vben5中，表单组件不是直接导入`VbenForm`组件，而是通过`useVbenForm`钩子函数来创建表单组件。

## 修正过程

### 1. 修正导入方式

**修正前：**

```typescript
import { useVbenDrawer, VbenDrawer } from '@vben/common-ui';
import { VbenForm } from '#/adapter/form';
```

**修正后：**

```typescript
import { useVbenDrawer } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
```

### 2. 修正表单初始化

**修正前：**

```vue
<template>
  <VbenForm ref="formRef" :schema="drawerSchema()" />
</template>

<script>
const formRef = ref();
</script>
```

**修正后：**

```vue
<template>
  <BasicForm />
</template>

<script>
const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  schema: drawerSchema(),
  showDefaultActions: false,
});
</script>
```

### 3. 修正表单API调用

**修正前：**

```typescript
await formRef.value?.setValues(costCenter);
await formRef.value?.resetForm();
const values = await formRef.value?.validate();
```

**修正后：**

```typescript
await formApi.setValues(costCenter);
await formApi.resetForm();
const values = await formApi.validate();
```

### 4. 修正组件命名冲突

**修正前：**

```typescript
const [VbenDrawer, drawerApi] = useVbenDrawer({...});
```

**修正后：**

```typescript
const [DrawerComponent, drawerApi] = useVbenDrawer({...});
```

**模板中对应修正：**

```vue
<template>
  <DrawerComponent>
    <!-- 内容 -->
  </DrawerComponent>
</template>
```

## vben5 表单使用的正确方式

### 1. 基本用法

```typescript
import { useVbenForm } from '#/adapter/form';

const [Form, formApi] = useVbenForm({
  schema: [
    {
      component: 'Input',
      fieldName: 'name',
      label: '名称',
      rules: 'required',
    },
  ],
  showDefaultActions: false,
});
```

### 2. 表单API方法

- `formApi.setValues(values)` - 设置表单值
- `formApi.resetForm()` - 重置表单
- `formApi.validate()` - 验证表单
- `formApi.getValues()` - 获取表单值
- `formApi.updateSchema(schema)` - 更新表单结构

### 3. 在Drawer中使用

```vue
<template>
  <DrawerComponent>
    <Form />
  </DrawerComponent>
</template>

<script setup>
import { useVbenDrawer } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';

const [Form, formApi] = useVbenForm({
  schema: formSchema(),
  showDefaultActions: false,
});

const [DrawerComponent, drawerApi] = useVbenDrawer({
  onOpenChange: async (isOpen) => {
    if (isOpen) {
      // 打开时的逻辑
      await formApi.setValues(data);
    }
  },
});
</script>
```

## 注意事项

1. **不要直接导入VbenForm组件**：vben5中使用`useVbenForm`钩子
2. **避免命名冲突**：当同时使用多个use钩子时，注意组件命名
3. **表单验证**：使用`formApi.validate()`而不是ref调用
4. **表单重置**：使用`formApi.resetForm()`而不是ref调用
5. **动态更新**：使用`formApi.updateSchema()`更新表单结构

## 相关文档

- [vben5 表单文档](https://doc.vben.pro/components/common-ui/vben-form.html)
- [useVbenForm API](https://doc.vben.pro/components/common-ui/vben-form.html#api)

## 修正结果

✅ 成功修正了VbenForm导入错误✅ 表单组件正常工作✅ 表单API调用正确✅ 没有TypeScript错误

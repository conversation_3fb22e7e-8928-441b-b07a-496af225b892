import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { h } from 'vue';

import { getPopupContainer } from '@vben/utils';

import { Tag } from 'ant-design-vue';

export const contactQuerySchema: FormSchemaGetter = () => [
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: [], // 这里需要根据实际情况配置成本中心选项
    },
    fieldName: 'costCenterId',
    label: '成本中心',
  },
  {
    component: 'Input',
    fieldName: 'contactName',
    label: '联系人姓名',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: [
        { label: '男', value: '男' },
        { label: '女', value: '女' },
      ],
    },
    fieldName: 'contactGender',
    label: '性别',
  },
];

export const contactColumns: VxeGridProps['columns'] = [
  {
    title: '成本中心名称',
    field: 'costCenterName',
    width: 150,
  },
  {
    title: '联系人姓名',
    field: 'contactName',
    width: 120,
  },
  {
    title: '性别',
    field: 'contactGender',
    width: 80,
    slots: {
      default: ({ row }) => {
        const color = row.contactGender === '男' ? 'blue' : 'pink';
        return h(Tag, { color }, () => row.contactGender);
      },
    },
  },
  {
    title: '手机号',
    field: 'contactPhone',
    width: 120,
  },
  {
    title: '邮箱',
    field: 'contactEmail',
    width: 180,
  },
  {
    title: '岗位',
    field: 'contactPosition',
    width: 120,
  },
  {
    title: '主要联系人',
    field: 'isPrimary',
    width: 100,
    slots: {
      default: ({ row }) => {
        const color = row.isPrimary === 'Y' ? 'success' : 'default';
        const text = row.isPrimary === 'Y' ? '是' : '否';
        return h(Tag, { color }, () => text);
      },
    },
  },
  {
    title: '状态',
    field: 'status',
    width: 80,
    slots: {
      default: ({ row }) => {
        const color = row.status === '0' ? 'success' : 'error';
        const text = row.status === '0' ? '正常' : '停用';
        return h(Tag, { color }, () => text);
      },
    },
  },
  {
    title: '创建时间',
    field: 'createTime',
    width: 180,
  },
];

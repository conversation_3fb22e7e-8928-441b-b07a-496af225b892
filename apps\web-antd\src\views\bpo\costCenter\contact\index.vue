<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';

import { Page } from '@vben/common-ui';

import { Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { exportContact, getContactList } from '#/api/bpo/costCenter';
import { $t } from '#/locales';
import { commonDownloadExcel } from '#/utils/file/download';

import { contactColumns, contactQuerySchema } from './data';

defineOptions({ name: 'CostCenterContact' });

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 120,
    componentProps: {
      allowClear: true,
    },
  },
  schema: contactQuerySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  columns: contactColumns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await getContactList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'id',
  },
  id: 'bpo-cost-center-contact-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

function handleExport() {
  commonDownloadExcel(
    exportContact,
    '成本中心联系人信息',
    tableApi.formApi.form.values,
  );
}
</script>

<template>
  <Page :auto-content-height="true" content-class="flex flex-col w-full">
    <BasicTable class="flex-1 overflow-hidden" table-title="成本中心联系人列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            v-access:code="['bpo:costCenterContact:export']"
            @click="handleExport"
          >
            {{ $t('pages.common.export') }}
          </a-button>
        </Space>
      </template>
    </BasicTable>
  </Page>
</template>

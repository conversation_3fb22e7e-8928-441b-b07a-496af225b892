import { h } from 'vue';

import { BasicColumn, FormSchema } from '@/components/Table';
import { Tag } from 'ant-design-vue';

// 联系人表格列定义
export const contactColumns = [
  {
    title: '联系人姓名',
    dataIndex: 'contactName',
    key: 'contactName',
    width: 120,
  },
  {
    title: '性别',
    dataIndex: 'contactGender',
    key: 'contactGender',
    width: 80,
  },
  {
    title: '手机号',
    dataIndex: 'contactPhone',
    key: 'contactPhone',
    width: 120,
  },
  {
    title: '邮箱',
    dataIndex: 'contactEmail',
    key: 'contactEmail',
    width: 150,
  },
  {
    title: '岗位',
    dataIndex: 'contactPosition',
    key: 'contactPosition',
    width: 100,
  },
  {
    title: '主要联系人',
    dataIndex: 'isPrimary',
    key: 'isPrimary',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 80,
  },
];

export const columns: BasicColumn[] = [
  {
    title: '成本中心名称',
    dataIndex: 'costCenterName',
    width: 150,
  },
  {
    title: '成本中心编号',
    dataIndex: 'costCenterCode',
    width: 120,
  },
  {
    title: '客户名称',
    dataIndex: 'customerName',
    width: 120,
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
    width: 150,
  },
  {
    title: '所属部门',
    dataIndex: 'departmentName',
    width: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    format: 'date|YYYY-MM-DD HH:mm:ss',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'costCenterName',
    label: '成本中心名称',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'costCenterCode',
    label: '成本中心编号',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'customerId',
    label: '客户',
    component: 'ApiSelect',
    componentProps: {
      api: () => import('@/api/bpo/customer').then((m) => m.getCustomerList()),
      labelField: 'customerName',
      valueField: 'id',
    },
    colProps: { span: 8 },
  },
];

export const costCenterFormSchema: FormSchema[] = [
  {
    field: 'customerId',
    label: '客户',
    component: 'ApiSelect',
    componentProps: {
      api: () => import('@/api/bpo/customer').then((m) => m.getCustomerList()),
      labelField: 'customerName',
      valueField: 'id',
      onChange: (value: number) => {
        // 当客户改变时，清空项目选择
        return { projectId: undefined };
      },
    },
    rules: [
      {
        required: true,
        message: '请选择客户',
      },
    ],
  },
  {
    field: 'projectId',
    label: '项目',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => {
          if (formModel.customerId) {
            return import('@/api/bpo/project').then((m) =>
              m.getProjectByCustomer(formModel.customerId),
            );
          }
          return Promise.resolve([]);
        },
        labelField: 'projectName',
        valueField: 'id',
        disabled: !formModel.customerId,
      };
    },
    rules: [
      {
        required: true,
        message: '请选择项目',
      },
    ],
  },
  {
    field: 'costCenterName',
    label: '成本中心名称',
    component: 'Input',
    rules: [
      {
        required: true,
        message: '请输入成本中心名称',
      },
      {
        max: 100,
        message: '成本中心名称长度不能超过100个字符',
      },
    ],
  },
  {
    field: 'costCenterCode',
    label: '成本中心编号',
    component: 'Input',
    rules: [
      {
        required: true,
        message: '请输入成本中心编号',
      },
      {
        max: 50,
        message: '成本中心编号长度不能超过50个字符',
      },
    ],
  },
  {
    field: 'departmentId',
    label: '所属部门',
    component: 'TreeSelect',
    componentProps: {
      api: () => import('@/api/system/dept').then((m) => m.getDeptList()),
      fieldNames: {
        label: 'deptName',
        key: 'deptId',
        value: 'deptId',
      },
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入备注',
      rows: 4,
    },
    colProps: { span: 24 },
    rules: [
      {
        max: 500,
        message: '备注长度不能超过500个字符',
      },
    ],
  },
];

export const contactColumns: BasicColumn[] = [
  {
    title: '联系人姓名',
    dataIndex: 'contactName',
    width: 120,
  },
  {
    title: '性别',
    dataIndex: 'contactGender',
    width: 80,
    customRender: ({ record }) => {
      const color = record.contactGender === '男' ? 'blue' : 'pink';
      return h(Tag, { color }, () => record.contactGender);
    },
  },
  {
    title: '手机号',
    dataIndex: 'contactPhone',
    width: 120,
  },
  {
    title: '邮箱',
    dataIndex: 'contactEmail',
    width: 150,
  },
  {
    title: '岗位',
    dataIndex: 'contactPosition',
    width: 100,
  },
  {
    title: '主要联系人',
    dataIndex: 'isPrimary',
    width: 100,
    customRender: ({ record }) => {
      const color = record.isPrimary === 'Y' ? 'success' : 'default';
      const text = record.isPrimary === 'Y' ? '是' : '否';
      return h(Tag, { color }, () => text);
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    customRender: ({ record }) => {
      const color = record.status === '0' ? 'success' : 'error';
      const text = record.status === '0' ? '正常' : '停用';
      return h(Tag, { color }, () => text);
    },
  },
];

import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getPopupContainer } from '@vben/utils';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'costCenterName',
    label: '成本中心名称',
  },
  {
    component: 'Input',
    fieldName: 'costCenterCode',
    label: '成本中心编号',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: [], // 这里需要根据实际情况配置客户选项
    },
    fieldName: 'customerId',
    label: '客户',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '成本中心名称',
    field: 'costCenterName',
    width: 150,
  },
  {
    title: '成本中心编号',
    field: 'costCenterCode',
    width: 120,
  },
  {
    title: '客户名称',
    field: 'customerName',
    width: 120,
  },
  {
    title: '项目名称',
    field: 'projectName',
    width: 150,
  },
  {
    title: '所属部门',
    field: 'departmentName',
    width: 120,
  },
  {
    title: '创建时间',
    field: 'createTime',
    width: 180,
  },
  {
    title: '备注',
    field: 'remark',
    width: 200,
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    resizable: false,
    width: 'auto',
  },
];

import type { FormSchemaGetter } from '#/adapter/form';

import { getPopupContainer } from '@vben/utils';

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'id',
    label: 'id',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: [], // 这里需要根据实际情况配置客户选项
    },
    fieldName: 'customerId',
    label: '客户',
    rules: 'selectRequired',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: [], // 这里需要根据实际情况配置项目选项
    },
    fieldName: 'projectId',
    label: '项目',
    rules: 'selectRequired',
  },
  {
    component: 'Input',
    fieldName: 'costCenterName',
    label: '成本中心名称',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'costCenterCode',
    label: '成本中心编号',
    rules: 'required',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      getPopupContainer,
    },
    fieldName: 'departmentId',
    label: '所属部门',
  },
  {
    component: 'Textarea',
    fieldName: 'remark',
    formItemClass: 'items-start',
    label: '备注',
  },
];

// 联系人表格列定义
export const contactColumns = [
  {
    title: '联系人姓名',
    dataIndex: 'contactName',
    key: 'contactName',
    width: 120,
  },
  {
    title: '性别',
    dataIndex: 'contactGender',
    key: 'contactGender',
    width: 80,
  },
  {
    title: '手机号',
    dataIndex: 'contactPhone',
    key: 'contactPhone',
    width: 120,
  },
  {
    title: '邮箱',
    dataIndex: 'contactEmail',
    key: 'contactEmail',
    width: 150,
  },
  {
    title: '岗位',
    dataIndex: 'contactPosition',
    key: 'contactPosition',
    width: 100,
  },
  {
    title: '主要联系人',
    dataIndex: 'isPrimary',
    key: 'isPrimary',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 80,
  },
];

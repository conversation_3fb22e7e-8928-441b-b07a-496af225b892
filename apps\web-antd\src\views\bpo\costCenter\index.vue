<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { CostCenter } from '#/api/bpo/costCenter/model';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import {
  Button as GhostButton,
  Modal,
  Popconfirm,
  Space,
} from 'ant-design-vue';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import {
  delCostCenter,
  exportCostCenter,
  getCostCenterList,
} from '#/api/bpo/costCenter';
import { $t } from '#/locales';
import { commonDownloadExcel } from '#/utils/file/download';

import costCenterDrawer from './CostCenterModal.vue';
import { columns, querySchema } from './data';

defineOptions({ name: 'CostCenter' });

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 120,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    highlight: true,
    reserve: true,
    trigger: 'cell',
  },
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await getCostCenterList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'id',
  },
  id: 'bpo-cost-center-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [CostCenterDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: costCenterDrawer,
});

function handleAdd() {
  drawerApi.setData({});
  drawerApi.open();
}

async function handleEdit(record: CostCenter) {
  drawerApi.setData({ id: record.id });
  drawerApi.open();
}

async function handleDelete(row: CostCenter) {
  await delCostCenter([row.id!]);
  await tableApi.query();
}

function handleMultiDelete() {
  const rows = tableApi.grid.getCheckboxRecords();
  const ids = rows.map((row: CostCenter) => row.id!);
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await delCostCenter(ids);
      await tableApi.query();
    },
  });
}

function handleExport() {
  commonDownloadExcel(
    exportCostCenter,
    '成本中心信息',
    tableApi.formApi.form.values,
  );
}
</script>

<template>
  <Page :auto-content-height="true" content-class="flex flex-col w-full">
    <BasicTable class="flex-1 overflow-hidden" table-title="成本中心列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            v-access:code="['bpo:costCenter:export']"
            @click="handleExport"
          >
            {{ $t('pages.common.export') }}
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['bpo:costCenter:remove']"
            @click="handleMultiDelete"
          >
            {{ $t('pages.common.delete') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['bpo:costCenter:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <GhostButton
            v-access:code="['bpo:costCenter:edit']"
            @click="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </GhostButton>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <GhostButton
              danger
              v-access:code="['bpo:costCenter:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </GhostButton>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <CostCenterDrawer @reload="tableApi.query()" />
  </Page>
</template>

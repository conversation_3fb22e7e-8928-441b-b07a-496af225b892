# 客户信息API提交调试指南

## 问题描述

编辑客户信息时，客户相关属性没有提交到API接口。

## 已修复的问题

### 1. 数据清理逻辑过于严格

**问题**: 使用`Object.fromEntries`和`filter`清理数据时，可能过滤掉有效数据 **修复**: 移除过度的数据清理，直接使用构建的customerBo对象

### 2. 表单数据设置问题

**问题**: 使用`...customer`展开可能包含不需要的字段 **修复**: 明确指定表单需要的字段

### 3. 类型定义问题

**问题**: CustomerBo的customerName字段定义为必填，可能导致类型错误 **修复**: 改为可选字段，在运行时验证

## 调试步骤

### 1. 打开浏览器开发者工具

1. 按 `F12` 打开开发者工具
2. 切换到 `Console` 标签页
3. 清空控制台日志

### 2. 测试编辑功能

1. 打开客户列表页面
2. 点击某个客户的"编辑"按钮
3. 修改客户信息
4. 点击"确认"按钮
5. 观察控制台输出

### 3. 检查控制台日志

#### 正常情况下应该看到：

```
抽屉打开，数据: {id: 1, ...}
获取到的部门树数据: [...]
加载客户数据，ID: 1
获取到的客户数据: {id: 1, customerName: "...", ...}
设置表单数据: {id: 1, customerName: "...", deptId: 123, remark: "..."}
开始表单验证...
表单验证通过，原始表单数据: {customerName: "...", deptId: 123, remark: "..."}
表单字段详情:
- customerName: "客户名称"
- deptId: 123
- remark: "备注信息"
- id: 1
构建的客户数据对象: {customerName: "...", departmentId: "123", remark: "...", id: 1}
JSON字符串: {
  "customerName": "客户名称",
  "departmentId": "123",
  "remark": "备注信息",
  "id": 1
}
调用更新客户API: /bpo/customer {customerName: "...", departmentId: "123", remark: "...", id: 1}
```

### 4. 检查Network标签页

1. 切换到 `Network` 标签页
2. 清空网络日志
3. 执行保存操作
4. 查找 `PUT /bpo/customer` 请求
5. 检查请求详情

#### 检查请求内容：

- **Request URL**: `http://localhost:8080/bpo/customer`
- **Request Method**: `PUT`
- **Request Headers**: 包含 `Content-Type: application/json`
- **Request Payload**:
  ```json
  {
    "id": 1,
    "customerName": "客户名称",
    "departmentId": "123",
    "remark": "备注信息"
  }
  ```

### 5. 检查响应内容

- **Status Code**: 应该是 200
- **Response**:
  ```json
  {
    "code": 200,
    "msg": "操作成功",
    "data": null
  }
  ```

## 常见问题排查

### 问题1: 表单验证失败

**现象**: 控制台显示验证错误，没有"表单验证通过"日志 **原因**: 必填字段未填写或格式不正确 **解决**: 确保客户名称已填写且不为空

### 问题2: 表单数据为空

**现象**: "原始表单数据"显示为空对象 `{}` **原因**: 表单字段配置问题或表单未正确初始化 **解决**: 检查drawer-data.ts中的字段配置

### 问题3: API请求未发送

**现象**: Network标签页中没有看到API请求 **原因**: 代码执行到API调用前就出错了 **解决**: 检查控制台错误信息

### 问题4: API请求发送但数据为空

**现象**: 看到API请求但Request Payload为空 **原因**: 数据构建逻辑有问题 **解决**: 检查customerBo对象的构建过程

### 问题5: 服务器返回错误

**现象**: API请求发送成功但返回4xx或5xx错误 **原因**: 后端验证失败或服务器错误 **解决**: 检查Response内容中的错误信息

## 数据流转检查点

### 1. 表单初始化

```
后端数据 → 表单数据转换 → 表单显示
```

检查点：`设置表单数据` 日志

### 2. 表单验证

```
用户输入 → 表单验证 → 验证通过
```

检查点：`表单验证通过，原始表单数据` 日志

### 3. 数据构建

```
表单数据 → CustomerBo对象 → API调用
```

检查点：`构建的客户数据对象` 和 `JSON字符串` 日志

### 4. API调用

```
CustomerBo对象 → HTTP请求 → 服务器响应
```

检查点：`调用更新客户API` 日志和Network标签页

## 后端接口要求

### 更新客户接口 (PUT /bpo/customer)

```json
{
  "id": 1, // 必填：客户ID
  "customerName": "客户名称", // 必填：客户名称
  "departmentId": "123", // 可选：部门ID
  "remark": "备注信息" // 可选：备注信息
}
```

### 期望响应格式

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

## 解决方案总结

1. **移除过度的数据清理**: 直接使用构建的customerBo对象
2. **明确表单字段映射**: 只设置表单需要的字段
3. **增强调试信息**: 添加详细的控制台日志
4. **优化错误处理**: 在数据构建时进行必要的验证

## 测试建议

1. **基本功能测试**: 新增、编辑、删除客户
2. **边界测试**: 空值、特殊字符、长文本
3. **网络测试**: 断网、超时、服务器错误
4. **权限测试**: 不同用户权限下的操作

现在客户信息应该能够正确提交到API接口了！

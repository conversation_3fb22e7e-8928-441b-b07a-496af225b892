# 客户编辑保存问题调试指南

## 问题描述

编辑客户信息时无法保存。

## 已修复的问题

### 1. API导入路径问题

**问题**: API导入路径可能不正确 **修复**:

- 创建了 `#/api/bpo/customer.ts` 文件重新导出API
- 统一使用 `#/api/bpo/customer` 导入路径

### 2. 错误处理增强

**问题**: 缺少详细的错误信息 **修复**: 在 `handleSubmit` 函数中添加了详细的错误处理和日志

```typescript
async function handleSubmit() {
  try {
    const values = await formApi.validate();
    console.log('表单验证通过，提交数据:', values);

    // ... 处理逻辑
  } catch (error) {
    console.error('保存客户信息失败:', error);
    message.error('保存失败: ' + (error?.message || '未知错误'));
  }
}
```

### 3. 部门树数据调试

**问题**: 部门选择可能有问题 **修复**: 添加了部门树数据的控制台日志

## 调试步骤

### 1. 打开浏览器开发者工具

1. 按 `F12` 打开开发者工具
2. 切换到 `Console` 标签页
3. 清空控制台日志

### 2. 测试保存功能

1. 打开客户编辑页面
2. 填写必要信息（客户名称是必填项）
3. 选择部门（可选）
4. 点击保存按钮
5. 观察控制台输出

### 3. 检查控制台日志

#### 正常情况下应该看到：

```
获取到的部门树数据: [...]
表单验证通过，提交数据: { customerName: "...", deptId: 123, remark: "..." }
准备提交的客户数据: { customerName: "...", departmentIds: "123", remark: "..." }
新增客户 (或 更新客户，ID: 123)
```

#### 如果出现错误，可能看到：

```
保存客户信息失败: Error: ...
```

### 4. 常见问题排查

#### 问题1: 表单验证失败

**现象**: 控制台没有 "表单验证通过" 日志 **原因**: 必填字段未填写 **解决**: 确保客户名称已填写

#### 问题2: API请求失败

**现象**: 控制台显示网络错误或404错误 **原因**: 后端API不存在或路径错误 **解决**:

1. 检查后端服务是否启动
2. 确认API路径是否正确：
   - 新增: `POST /bpo/customer`
   - 更新: `PUT /bpo/customer`

#### 问题3: 部门数据加载失败

**现象**: 控制台显示 "获取到的部门树数据: []" 或错误 **原因**: 部门API不可用或权限不足 **解决**:

1. 检查是否有访问部门API的权限
2. 确认 `/system/user/deptTree` 接口可用

#### 问题4: 数据格式错误

**现象**: 后端返回400错误 **原因**: 提交的数据格式不符合后端要求 **解决**: 检查后端期望的数据格式

## 网络请求检查

### 1. 打开Network标签页

1. 在开发者工具中切换到 `Network` 标签
2. 清空网络日志
3. 执行保存操作

### 2. 检查API请求

查找以下请求：

- `POST /bpo/customer` (新增)
- `PUT /bpo/customer` (更新)
- `GET /system/user/deptTree` (部门树)

### 3. 检查请求详情

点击具体的请求，查看：

- **Request Headers**: 确认Content-Type和Authorization
- **Request Payload**: 确认提交的数据格式
- **Response**: 查看服务器返回的错误信息

## 后端接口要求

### 新增客户 (POST /bpo/customer)

```json
{
  "customerName": "客户名称",
  "departmentIds": "123",
  "remark": "备注信息"
}
```

### 更新客户 (PUT /bpo/customer)

```json
{
  "id": 1,
  "customerName": "客户名称",
  "departmentIds": "123",
  "remark": "备注信息"
}
```

### 期望的响应格式

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

## 权限检查

确保当前用户具有以下权限：

- `bpo:customer:add` - 新增客户
- `bpo:customer:edit` - 编辑客户
- `system:user:list` - 查看部门树（如果需要）

## 联系开发者

如果以上步骤都无法解决问题，请提供以下信息：

1. 控制台完整的错误日志
2. Network标签页中的请求详情
3. 具体的操作步骤
4. 浏览器版本和操作系统信息

## 临时解决方案

如果部门选择有问题，可以暂时：

1. 不选择部门，只填写客户名称和备注
2. 保存后再编辑添加部门信息

## 代码位置

相关文件位置：

- 客户编辑组件: `apps\web-antd\src\views\bpo\customer\customer-drawer.vue`
- API接口: `apps\web-antd\src\api\bpo\customer\index.ts`
- 数据配置: `apps\web-antd\src\views\bpo\customer\drawer-data.ts`

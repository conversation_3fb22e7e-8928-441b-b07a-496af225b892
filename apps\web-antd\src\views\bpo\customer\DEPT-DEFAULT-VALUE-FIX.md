# 部门默认值问题修复

## 问题描述

编辑客户时，如果客户的所属部门为空，表单中的部门选择器不应该有默认值，应该显示为空。

## 问题分析

### 可能的原因

1. **updateSchema时机问题**: `formApi.updateSchema`可能会影响表单值
2. **TreeSelect默认行为**: TreeSelect组件可能有默认选中行为
3. **数据转换问题**: `getDeptIdForForm`函数可能没有正确处理空值
4. **表单初始化顺序**: 部门选择器初始化和表单值设置的顺序问题

## 已实施的修复

### 1. 调整初始化顺序

**修复前**:

```typescript
// 先设置表单数据，再初始化部门选择
await formApi.setValues(formData);
await setupDeptSelect();
```

**修复后**:

```typescript
// 先初始化部门选择，再设置表单数据
await setupDeptSelect();
await formApi.setValues(formData);
```

### 2. 增强TreeSelect配置

```typescript
componentProps: {
  // ... 其他配置
  allowClear: true,           // 允许清空
  value: undefined,           // 明确设置值为undefined
  defaultValue: undefined,    // 明确设置默认值为undefined
},
fieldName: 'deptId',
defaultValue: undefined,      // 字段级别的默认值
```

### 3. 增强数据处理函数

```typescript
function getDeptIdForForm(departmentId: any): number | undefined {
  console.log('处理departmentId:', {
    value: departmentId,
    type: typeof departmentId,
  });

  // 如果是null、undefined或空字符串，返回undefined
  if (!departmentId || departmentId === '') {
    console.log('departmentId为空，返回undefined');
    return undefined;
  }

  // 详细的类型处理和日志输出...
}
```

### 4. 增加详细调试信息

```typescript
console.log('deptId处理结果:', {
  原始值: customer.departmentId,
  处理后: formData.deptId,
  是否为undefined: formData.deptId === undefined,
});
```

## 测试步骤

### 1. 测试无部门的客户编辑

1. 打开浏览器开发者工具 (F12)
2. 编辑一个没有部门的客户
3. 查看控制台输出

**期望的日志输出**:

```
departmentId详细信息: {
  value: null,  // 或 undefined 或 ""
  type: "object",
  isNull: true,
  isUndefined: false,
  isEmpty: false
}
处理departmentId: {value: null, type: "object"}
departmentId为空，返回undefined
deptId处理结果: {
  原始值: null,
  处理后: undefined,
  是否为undefined: true
}
设置表单数据: {id: 1, customerName: "...", deptId: undefined, remark: "..."}
设置后的表单值: {id: 1, customerName: "...", deptId: undefined, remark: "..."}
```

**期望的表单行为**:

- 部门选择器应该显示为空
- 占位符文本应该显示"请选择部门"
- 不应该有任何默认选中的部门

### 2. 测试有部门的客户编辑

1. 编辑一个有部门的客户
2. 查看控制台输出

**期望的日志输出**:

```
departmentId详细信息: {
  value: "123",
  type: "string",
  isNull: false,
  isUndefined: false,
  isEmpty: false
}
处理departmentId: {value: "123", type: "string"}
字符串类型处理结果: {trimmed: "123", num: 123, result: 123}
deptId处理结果: {
  原始值: "123",
  处理后: 123,
  是否为undefined: false
}
设置表单数据: {id: 1, customerName: "...", deptId: 123, remark: "..."}
设置后的表单值: {id: 1, customerName: "...", deptId: 123, remark: "..."}
```

**期望的表单行为**:

- 部门选择器应该显示对应的部门名称
- 应该正确回显部门选择

### 3. 测试新增客户

1. 点击"新增"按钮
2. 查看控制台输出

**期望的日志输出**:

```
新增模式，重置表单
重置后的表单值: {deptId: undefined, ...}
```

**期望的表单行为**:

- 所有字段应该为空
- 部门选择器应该显示占位符

## 可能的问题和解决方案

### 问题1: TreeSelect仍然显示默认值

**现象**: 即使deptId为undefined，TreeSelect仍然显示某个部门

**可能原因**:

- TreeSelect组件的内部状态没有正确更新
- 部门树数据中有默认选中的项

**解决方案**:

```typescript
// 在setValues之后强制清空TreeSelect
await formApi.setValues(formData);
if (formData.deptId === undefined) {
  await formApi.setFieldValue('deptId', undefined);
}
```

### 问题2: updateSchema影响表单值

**现象**: 调用updateSchema后表单值被重置

**解决方案**:

```typescript
// 保存当前表单值
const currentValues = await formApi.getValues();
// 更新schema
await setupDeptSelect();
// 恢复表单值
await formApi.setValues(currentValues);
```

### 问题3: 部门树数据问题

**现象**: 部门树中有预选的项

**解决方案**: 检查部门树数据，确保没有设置默认选中：

```typescript
// 检查部门树数据
console.log('部门树数据:', deptTree);
deptTree.forEach((dept) => {
  if (dept.selected || dept.checked) {
    console.warn('发现预选的部门:', dept);
  }
});
```

## 验证清单

### ✅ 无部门客户编辑

- [ ] 部门选择器显示为空
- [ ] 显示"请选择部门"占位符
- [ ] 控制台显示deptId为undefined
- [ ] 保存后不会设置部门

### ✅ 有部门客户编辑

- [ ] 部门选择器正确回显部门
- [ ] 控制台显示正确的deptId值
- [ ] 可以修改部门选择
- [ ] 保存后部门正确更新

### ✅ 新增客户

- [ ] 所有字段为空
- [ ] 部门选择器显示占位符
- [ ] 可以正常选择部门
- [ ] 保存后部门正确设置

### ✅ 边界情况

- [ ] departmentId为null时正确处理
- [ ] departmentId为""时正确处理
- [ ] departmentId为"0"时正确处理
- [ ] departmentId为0时正确处理

## 调试命令

### 在浏览器控制台中执行

```javascript
// 检查当前表单值
formApi.getValues().then((values) => console.log('当前表单值:', values));

// 检查特定字段值
formApi
  .getFieldValue('deptId')
  .then((value) => console.log('deptId值:', value));

// 手动设置字段值为undefined
formApi.setFieldValue('deptId', undefined);

// 检查TreeSelect组件状态
document.querySelector('[data-field="deptId"]');
```

## 总结

通过以下修复措施：

1. **调整初始化顺序**: 先初始化部门选择器，再设置表单值
2. **增强TreeSelect配置**: 明确设置不要默认值
3. **增强数据处理**: 确保空值正确转换为undefined
4. **增加调试信息**: 便于排查问题

现在编辑无部门的客户时，部门选择器应该正确显示为空，不会有任何默认值。

请按照测试步骤验证修复效果，如果仍有问题，请提供控制台的详细日志输出。

# 部门显示问题调试指南

## 问题描述

1. 列表中的所属部门没有显示
2. 所属部门为空时编辑客户表单不应有默认值

## 已实施的修复

### 1. 修正编辑表单的数据回显逻辑

**问题**: 当`departmentId`为空字符串时，`Number('')`返回`0`，导致表单显示错误的默认值

**修复前:**

```typescript
deptId: customer.departmentId ? Number(customer.departmentId) : undefined,
```

**修复后:**

```typescript
deptId: customer.departmentId && customer.departmentId.trim() !== ''
  ? Number(customer.departmentId)
  : undefined,
```

**说明**: 现在只有当`departmentId`存在且不为空字符串时才设置`deptId`，否则设置为`undefined`，确保表单不显示默认值。

### 2. 增加列表数据调试信息

在客户列表加载时添加了详细的调试信息：

```typescript
console.log('客户列表API返回数据:', result);
console.log('第一条客户数据示例:', result.records[0]);
console.log('部门相关字段:', {
  departmentId: result.records[0].departmentId,
  deptName: result.records[0].deptName,
});
```

### 3. 增加列表格式化调试信息

在部门列的formatter中添加调试信息：

```typescript
formatter({ cellValue, row }) {
  console.log('部门列格式化 - cellValue:', cellValue, 'row:', row);
  return cellValue || '暂无';
}
```

## 调试步骤

### 1. 检查后端返回数据

1. 打开浏览器开发者工具 (F12)
2. 切换到Console标签页
3. 刷新客户列表页面
4. 查看控制台输出

**期望看到的日志:**

```
客户列表API返回数据: {records: [...], total: 10}
第一条客户数据示例: {id: 1, customerName: "...", departmentId: "123", deptName: "技术部", ...}
部门相关字段: {departmentId: "123", deptName: "技术部"}
```

**如果看到的是:**

```
部门相关字段: {departmentId: "123", deptName: undefined}
```

说明后端没有返回`deptName`字段。

### 2. 检查列表显示

查看控制台中的部门列格式化日志：

```
部门列格式化 - cellValue: "技术部" row: {id: 1, customerName: "...", ...}
```

**如果cellValue为undefined或null，说明:**

- 后端没有返回`deptName`字段
- 字段名不匹配
- 数据格式有问题

### 3. 检查编辑表单

1. 点击编辑某个客户
2. 查看控制台输出的表单数据

**有部门的客户:**

```
设置表单数据: {id: 1, customerName: "...", deptId: 123, remark: "..."}
```

**无部门的客户:**

```
设置表单数据: {id: 1, customerName: "...", deptId: undefined, remark: "..."}
```

## 可能的问题和解决方案

### 问题1: 后端没有返回deptName字段

**现象**: 控制台显示`deptName: undefined`

**原因**: 后端BpoCustomerVo没有包含部门名称字段

**解决方案**:

1. 检查后端BpoCustomerVo是否包含deptName字段
2. 确认后端查询时是否关联了部门表
3. 可能需要修改后端代码添加部门名称查询

### 问题2: 字段名不匹配

**现象**: 后端返回了部门名称，但字段名不是`deptName`

**可能的字段名**:

- `departmentName`
- `deptName`
- `dept_name`
- `department_name`

**解决方案**: 修改前端列配置中的field名称

```typescript
{
  title: '所属部门',
  field: 'departmentName', // 修改为实际的字段名
  width: 200,
  formatter({ cellValue }) {
    return cellValue || '暂无';
  },
}
```

### 问题3: 部门ID为字符串"0"

**现象**: 编辑时显示了不应该有的默认值

**原因**: 后端返回的departmentId为字符串"0"而不是空字符串

**解决方案**: 修改判断逻辑

```typescript
deptId: customer.departmentId &&
        customer.departmentId.trim() !== '' &&
        customer.departmentId !== '0'
  ? Number(customer.departmentId)
  : undefined,
```

### 问题4: 数据类型问题

**现象**: departmentId是数字类型而不是字符串

**解决方案**: 修改类型判断

```typescript
deptId: customer.departmentId && customer.departmentId > 0
  ? customer.departmentId
  : undefined,
```

## 后端数据要求

### 1. 客户列表接口响应格式

```json
{
  "code": 200,
  "data": {
    "records": [
      {
        "id": 1,
        "customerName": "客户名称",
        "departmentId": "123",
        "deptName": "技术部", // 必须包含此字段用于列表显示
        "remark": "备注信息",
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 1
  }
}
```

### 2. 客户详情接口响应格式

```json
{
  "code": 200,
  "data": {
    "id": 1,
    "customerName": "客户名称",
    "departmentId": "123", // 用于编辑表单
    "remark": "备注信息"
  }
}
```

### 3. 空部门的处理

**有部门的客户:**

```json
{
  "departmentId": "123",
  "deptName": "技术部"
}
```

**无部门的客户:**

```json
{
  "departmentId": null, // 或者 "" 或者不包含此字段
  "deptName": null // 或者 "" 或者不包含此字段
}
```

## 临时解决方案

### 1. 如果后端暂时无法提供deptName字段

可以在前端根据departmentId查询部门名称：

```typescript
formatter({ cellValue, row }) {
  if (row.departmentId) {
    // 根据departmentId查询部门名称
    // 这需要额外的API调用，不推荐
    return '部门ID: ' + row.departmentId;
  }
  return '暂无';
}
```

### 2. 如果字段名不确定

可以尝试多个可能的字段名：

```typescript
formatter({ cellValue, row }) {
  const deptName = row.deptName || row.departmentName || row.dept_name || row.department_name;
  return deptName || '暂无';
}
```

## 测试验证

### 1. 列表显示测试

- ✅ 有部门的客户显示部门名称
- ✅ 无部门的客户显示"暂无"
- ✅ 控制台无错误信息

### 2. 编辑表单测试

- ✅ 有部门的客户编辑时正确回显部门
- ✅ 无部门的客户编辑时部门选择为空
- ✅ 部门选择器正常工作

### 3. 数据保存测试

- ✅ 选择部门后保存成功
- ✅ 清空部门后保存成功
- ✅ 列表刷新后显示正确

请按照调试步骤检查控制台输出，这将帮助我们确定具体的问题所在！

# 客户列表部门显示解决方案

## 问题描述

客户信息列表中的所属部门列显示为空。

## 参考实现

参考了 `D:\java\workspace\ruoyi-plus-vben5\apps\web-antd\src\views\system\user` 模块的部门显示方式。

## system/user模块的实现方式

### 1. 列配置 (data.tsx)

```typescript
{
  field: 'deptName',
  title: '部门',
  minWidth: 120,
}
```

### 2. 数据模型 (model.d.ts)

```typescript
export interface User {
  userId: string;
  deptId: number;
  deptName: string; // 直接包含部门名称
  // ... 其他字段
}
```

### 3. 特点

- **简洁配置**: 不需要复杂的formatter
- **后端关联**: 后端查询时直接关联部门表
- **直接返回**: API直接返回部门名称字段

## 客户模块的修复方案

### 1. 前端修复 (已完成)

#### 增强列配置调试

```typescript
{
  title: '所属部门',
  field: 'deptName',
  minWidth: 120,
  formatter({ cellValue, row }) {
    console.log('部门列格式化 - cellValue:', cellValue, 'row:', row);
    console.log('可能的部门字段:', {
      deptName: row.deptName,
      departmentName: row.departmentName,
      dept_name: row.dept_name,
      department_name: row.department_name,
      deptId: row.deptId,
      departmentId: row.departmentId,
    });

    // 尝试多个可能的字段名
    const deptName = cellValue ||
                    row.departmentName ||
                    row.dept_name ||
                    row.department_name;

    return deptName || '暂无';
  },
}
```

#### 增强API调试

```typescript
query: async ({ page }, formValues = {}) => {
  const result = await getCustomerList({
    pageNum: page.currentPage,
    pageSize: page.pageSize,
    ...formValues,
  });

  console.log('客户列表API返回数据:', result);
  if (result?.records && result.records.length > 0) {
    console.log('第一条客户数据示例:', result.records[0]);
    console.log('部门相关字段:', {
      departmentId: result.records[0].departmentId,
      deptName: result.records[0].deptName,
    });
  }

  return result;
};
```

### 2. 后端修复 (需要实施)

#### 问题分析

根据调试信息，可能的问题：

1. **后端没有返回deptName字段**
2. **字段名不匹配**
3. **部门关联查询缺失**

#### 解决方案

##### 方案1: 修改BpoCustomerVo

在 `BpoCustomerVo.java` 中添加部门名称字段：

```java
public class BpoCustomerVo {
    private Long id;
    private String customerName;
    private String departmentId;
    private String deptName;        // 添加部门名称字段
    private String remark;
    private Date createTime;
    private Date updateTime;

    // getter/setter...
}
```

##### 方案2: 修改Mapper查询

在 `BpoCustomerMapper.xml` 中添加部门关联查询：

```xml
<select id="selectVoList" resultType="BpoCustomerVo">
    SELECT
        c.id,
        c.customer_name,
        c.department_id,
        c.remark,
        c.create_time,
        c.update_time,
        d.dept_name as deptName    -- 关联查询部门名称
    FROM bpo_customer c
    LEFT JOIN sys_dept d ON c.department_id = d.dept_id
    <where>
        <!-- 查询条件 -->
    </where>
</select>
```

##### 方案3: 修改Service层

在 `BpoCustomerServiceImpl.java` 中添加部门名称设置：

```java
@Override
public TableDataInfo<BpoCustomerVo> queryPageList(BpoCustomerBo bo, PageQuery pageQuery) {
    LambdaQueryWrapper<BpoCustomer> lqw = buildQueryWrapper(bo);
    Page<BpoCustomerVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

    // 设置部门名称
    result.getRecords().forEach(vo -> {
        if (StringUtils.isNotBlank(vo.getDepartmentId())) {
            SysDept dept = deptService.selectDeptById(Long.valueOf(vo.getDepartmentId()));
            if (dept != null) {
                vo.setDeptName(dept.getDeptName());
            }
        }
    });

    return TableDataInfo.build(result);
}
```

## 调试步骤

### 1. 检查前端调试信息

1. 打开浏览器开发者工具 (F12)
2. 刷新客户列表页面
3. 查看控制台输出

**期望看到的信息:**

```
客户列表API返回数据: {records: [...], total: 10}
第一条客户数据示例: {id: 1, customerName: "...", departmentId: "123", deptName: "技术部"}
部门相关字段: {departmentId: "123", deptName: "技术部"}
部门列格式化 - cellValue: "技术部"
```

**如果看到的是:**

```
部门相关字段: {departmentId: "123", deptName: undefined}
部门列格式化 - cellValue: undefined
可能的部门字段: {deptName: undefined, departmentName: undefined, ...}
```

说明后端没有返回部门名称字段。

### 2. 检查后端日志

查看后端日志，确认：

1. SQL查询是否包含部门关联
2. 返回的JSON数据是否包含deptName字段

### 3. 检查数据库

直接查询数据库，确认：

1. 客户表中的department_id是否有值
2. 部门表中是否存在对应的部门记录

```sql
-- 检查客户和部门关联
SELECT
    c.id,
    c.customer_name,
    c.department_id,
    d.dept_name
FROM bpo_customer c
LEFT JOIN sys_dept d ON c.department_id = d.dept_id
LIMIT 10;
```

## 临时解决方案

### 1. 前端根据departmentId查询部门名称

```typescript
// 在formatter中异步查询部门名称（不推荐，性能差）
formatter({ cellValue, row }) {
  if (row.departmentId && !cellValue) {
    // 根据departmentId查询部门名称
    getDeptById(row.departmentId).then(dept => {
      // 更新显示
    });
  }
  return cellValue || '暂无';
}
```

### 2. 使用departmentId显示

```typescript
formatter({ cellValue, row }) {
  if (row.departmentId && !cellValue) {
    return `部门ID: ${row.departmentId}`;
  }
  return cellValue || '暂无';
}
```

## 推荐解决方案

### 最佳实践 (参考system/user)

1. **后端修改**: 在查询客户列表时关联部门表，直接返回deptName字段
2. **前端简化**: 使用简单的列配置，不需要复杂的formatter
3. **性能优化**: 一次查询获取所有数据，避免N+1查询问题

### 实施步骤

1. **立即**: 使用当前的调试版本查看后端返回的数据格式
2. **短期**: 根据调试信息确定具体问题并修复后端
3. **长期**: 简化前端配置，移除调试代码

## 数据格式对比

### system/user返回格式

```json
{
  "records": [
    {
      "userId": "1",
      "userName": "admin",
      "deptId": 103,
      "deptName": "研发部门"
    }
  ]
}
```

### 客户模块期望格式

```json
{
  "records": [
    {
      "id": 1,
      "customerName": "客户名称",
      "departmentId": "103",
      "deptName": "研发部门"
    }
  ]
}
```

## 总结

通过参考system/user模块的实现方式，客户列表的部门显示问题主要需要在后端解决：

1. 在查询时关联部门表
2. 返回deptName字段
3. 前端使用简单的列配置

当前的调试版本可以帮助确定具体的问题所在，请查看控制台输出以确定下一步的修复方向。

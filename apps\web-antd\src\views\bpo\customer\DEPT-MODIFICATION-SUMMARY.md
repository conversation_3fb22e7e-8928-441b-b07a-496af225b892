# 客户管理部门显示和选择方式修改总结

## 修改目标

参考 `D:\java\workspace\ruoyi-plus-vben5\apps\web-antd\src\views\system\user` 模块，修改客户管理中的部门显示方式和编辑中的部门选择方式。**部门选择改为单选模式**。

## 修改内容

### 1. 数据配置修改 (data.ts)

#### 查询表单简化

**修改前:**

```typescript
{
  component: 'TreeSelect',
  componentProps: {
    getPopupContainer,
    multiple: true,
    placeholder: '请选择部门',
    treeData: [],
  },
  fieldName: 'departmentIds',
  label: '所属部门',
}
```

**修改后:**

```typescript
// 移除了部门查询条件，简化查询表单
```

#### 列表显示优化

**修改前:**

```typescript
{
  title: '所属部门',
  field: 'departmentIds',
  width: 200,
}
```

**修改后:**

```typescript
{
  title: '所属部门',
  field: 'deptName',
  width: 200,
  formatter({ cellValue }) {
    return cellValue || '暂无';
  },
}
```

### 2. 抽屉数据配置修改 (drawer-data.ts)

**修改前:**

```typescript
{
  component: 'TreeSelect',
  componentProps: {
    getPopupContainer,
    multiple: true,
    placeholder: '请选择部门',
    treeData: [],
  },
  fieldName: 'departmentIds',
  label: '所属部门',
}
```

**修改后:**

```typescript
{
  component: 'TreeSelect',
  // 在drawer里更新 这里不需要默认的componentProps
  defaultValue: undefined,
  fieldName: 'deptId',
  label: '所属部门',
}
```

### 3. 抽屉组件修改 (customer-drawer.vue)

#### 新增导入

```typescript
import { addFullName, getPopupContainer } from '@vben/utils';
import { getDeptTree } from '#/api/system/user';
```

#### 新增部门树初始化函数

```typescript
/**
 * 初始化部门选择
 */
async function setupDeptSelect() {
  const deptTree = await getDeptTree();
  // 选中后显示在输入框的值 即父节点 / 子节点
  addFullName(deptTree, 'label', ' / ');
  formApi.updateSchema([
    {
      componentProps: {
        class: 'w-full',
        fieldNames: {
          key: 'id',
          value: 'id',
          children: 'children',
        },
        getPopupContainer,
        placeholder: '请选择部门',
        showSearch: true,
        treeData: deptTree,
        treeDefaultExpandAll: true,
        treeLine: { showLeafIcon: false },
        // 筛选的字段
        treeNodeFilterProp: 'label',
        // 选中后显示在输入框的值
        treeNodeLabelProp: 'fullName',
      },
      fieldName: 'deptId',
    },
  ]);
}
```

#### 修改数据回显逻辑

```typescript
if (isUpdate.value && data?.id) {
  const customer = await getCustomer(data.id);
  // 将departmentIds字符串转换为数字用于TreeSelect（单选）
  const formData = {
    ...customer,
    deptId: customer.departmentIds ? Number(customer.departmentIds) : undefined,
  };
  await formApi.setValues(formData);
}
```

#### 修改提交逻辑

```typescript
const customer: Customer = {
  ...values,
  // 将部门ID转换为字符串
  departmentIds: values.deptId ? String(values.deptId) : '',
};

// 删除临时字段
delete customer.deptId;
```

### 4. 类型定义修改 (model.d.ts)

**修改前:**

```typescript
export interface Customer extends BaseEntity {
  id?: number;
  customerName?: string;
  departmentIds?: string;
  remark?: string;
}
```

**修改后:**

```typescript
export interface Customer extends BaseEntity {
  id?: number;
  customerName?: string;
  departmentIds?: string;
  deptId?: number; // 用于表单编辑的部门ID（单选）
  deptName?: string; // 用于列表显示的部门名称
  remark?: string;
}
```

## 功能特性

### 1. 部门树选择

- ✅ **动态加载**: 从系统部门API获取部门树数据
- ✅ **单选模式**: 只能选择一个部门
- ✅ **搜索功能**: 支持按部门名称搜索
- ✅ **层级显示**: 显示完整的部门层级路径
- ✅ **展开控制**: 默认展开所有节点

### 2. 数据转换

- ✅ **存储格式**: 后端存储为字符串（单个部门ID）
- ✅ **编辑格式**: 前端编辑使用数字格式
- ✅ **显示格式**: 列表显示部门名称字符串

### 3. 用户体验

- ✅ **友好提示**: 无部门时显示"暂无"
- ✅ **层级路径**: 选择时显示完整路径（如：总公司 / 技术部 / 开发组）
- ✅ **快速搜索**: 支持部门名称模糊搜索

## 技术实现

### 1. 部门数据获取

```typescript
import { getDeptTree } from '#/api/system/user';
```

使用系统用户模块的部门树API，确保数据一致性。

### 2. 数据格式转换

- **编辑时**: `departmentIds` (string) → `deptId` (number)
- **保存时**: `deptId` (number) → `departmentIds` (string)

### 3. 树形组件配置

```typescript
fieldNames: {
  key: 'id',
  value: 'id',
  children: 'children',
}
```

## 后端数据要求

### 1. 客户列表接口

需要返回 `deptName` 字段，包含部门名称的显示文本。

### 2. 客户详情接口

返回 `departmentIds` 字段，格式为单个部门ID的字符串。

### 3. 保存接口

接收 `departmentIds` 字段，格式为单个部门ID的字符串。

## 注意事项

1. **API依赖**: 依赖系统用户模块的 `getDeptTree` API
2. **数据格式**: 确保前后端数据格式一致
3. **权限控制**: 需要有访问部门树的权限
4. **性能考虑**: 部门树数据会在每次打开抽屉时重新加载

## 测试建议

1. **功能测试**:
   - 测试部门树的加载和显示
   - 测试多部门选择功能
   - 测试搜索功能
   - 测试数据保存和回显

2. **边界测试**:
   - 测试无部门数据的情况
   - 测试大量部门数据的性能
   - 测试部门权限限制

3. **兼容性测试**:
   - 测试与现有客户数据的兼容性
   - 测试不同浏览器的兼容性

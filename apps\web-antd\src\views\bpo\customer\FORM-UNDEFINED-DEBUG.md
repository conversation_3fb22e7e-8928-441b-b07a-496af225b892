# 表单字段undefined问题调试指南

## 问题现象

表单提交时所有字段都显示为undefined：

```
- customerName: undefined
- deptId: undefined
- remark: undefined
- id: undefined
```

## 可能原因分析

### 1. 表单初始化时机问题

- 部门选择初始化可能影响其他字段
- 表单schema更新时机不当

### 2. 表单API使用问题

- `formApi.validate()` 返回空值
- `formApi.getValues()` 无法获取正确值

### 3. 表单字段配置问题

- fieldName配置错误
- 组件类型不匹配

## 已实施的修复

### 1. 优化表单初始化顺序

```typescript
// 修复前：先初始化部门选择，再设置表单值
await setupDeptSelect();
await formApi.setValues(formData);

// 修复后：根据模式分别处理
if (isUpdate.value) {
  await setupDeptSelect(); // 先初始化部门
  await formApi.setValues(formData); // 再设置值
} else {
  await setupDeptSelect(); // 先初始化部门
  await formApi.resetForm(); // 再重置表单
}
```

### 2. 增强表单验证逻辑

```typescript
// 获取当前表单值作为备用
const currentValues = formApi.getValues();

// 进行表单验证
const values = await formApi.validate();

// 使用验证结果或当前值
const finalValues = values || currentValues;
```

### 3. 改进表单schema配置

```typescript
{
  component: 'Input',
  fieldName: 'customerName',
  label: '客户名称',
  rules: 'required',
  componentProps: {
    placeholder: '请输入客户名称',
  },
}
```

## 调试步骤

### 1. 检查表单初始化

打开客户编辑抽屉，查看控制台日志：

```
抽屉打开，数据: {id: 1, ...}
获取到的部门树数据: [...]
加载客户数据，ID: 1
获取到的客户数据: {...}
设置表单数据: {id: 1, customerName: "...", ...}
设置后的表单值: {id: 1, customerName: "...", ...}
```

**检查点**：

- 部门树数据是否正确加载
- 客户数据是否正确获取
- 表单数据是否正确设置
- 设置后的表单值是否包含数据

### 2. 检查表单提交

点击确认按钮，查看控制台日志：

```
开始表单验证...
当前表单值: {customerName: "...", deptId: 123, ...}
表单验证通过，验证后数据: {...}
最终使用的表单数据: {...}
```

**检查点**：

- 当前表单值是否包含数据
- 表单验证是否成功
- 最终数据是否正确

### 3. 检查表单字段绑定

在浏览器开发者工具中：

1. 打开Elements标签页
2. 找到表单输入框
3. 检查input元素的value属性
4. 手动输入内容，观察是否能正常输入

## 常见问题排查

### 问题1: 表单值设置失败

**现象**: "设置后的表单值"显示空对象 **原因**:

- 表单schema配置错误
- fieldName不匹配
- 组件类型问题

**解决方案**:

```typescript
// 检查fieldName是否正确
{
  fieldName: 'customerName',  // 确保与数据字段一致
  component: 'Input',         // 确保组件类型正确
}
```

### 问题2: 部门选择影响其他字段

**现象**: 部门初始化后其他字段丢失 **原因**: updateSchema可能重置了表单

**解决方案**:

```typescript
// 在updateSchema后重新设置值
await setupDeptSelect();
await formApi.setValues(formData);
```

### 问题3: 表单验证返回空值

**现象**: validate()返回undefined或空对象 **原因**:

- 表单字段没有正确绑定
- 验证规则配置问题

**解决方案**:

```typescript
// 使用备用方案
const finalValues = values || currentValues;
```

### 问题4: 新增模式字段为空

**现象**: 新增时所有字段都是undefined **原因**: resetForm()可能清空了所有值

**解决方案**:

```typescript
// 新增模式不需要设置初始值，用户手动输入即可
await formApi.resetForm();
```

## 手动测试步骤

### 1. 新增客户测试

1. 点击"新增"按钮
2. 手动输入客户名称
3. 选择部门（可选）
4. 输入备注（可选）
5. 点击确认
6. 查看控制台日志

### 2. 编辑客户测试

1. 点击某个客户的"编辑"按钮
2. 检查字段是否正确回显
3. 修改某些字段
4. 点击确认
5. 查看控制台日志

### 3. 表单交互测试

1. 在表单中输入内容
2. 切换到控制台
3. 执行 `formApi.getValues()` 查看当前值
4. 检查是否能获取到输入的内容

## 临时解决方案

如果问题仍然存在，可以尝试：

### 1. 直接从DOM获取值

```typescript
// 获取DOM元素的值作为备用
const customerNameInput = document.querySelector('[data-field="customerName"]');
const customerName = customerNameInput?.value;
```

### 2. 使用事件监听

```typescript
// 监听表单字段变化
formApi.on('field-value-change', (field, value) => {
  console.log(`字段 ${field} 值变化为:`, value);
});
```

### 3. 延迟获取表单值

```typescript
// 延迟一段时间再获取表单值
await new Promise((resolve) => setTimeout(resolve, 100));
const values = formApi.getValues();
```

## 总结

表单字段undefined问题通常是由于：

1. 表单初始化时机不当
2. 表单API使用方式问题
3. 表单schema配置错误

通过增强调试信息和优化初始化逻辑，应该能够解决这个问题。如果问题仍然存在，请提供完整的控制台日志以便进一步分析。

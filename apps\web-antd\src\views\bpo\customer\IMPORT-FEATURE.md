# 客户管理导入功能实现

## 功能概述

为客户管理模块添加Excel导入功能，支持批量导入客户信息，参考system/user模块的实现方式。

## 参考实现

- **后台代码**: `D:\java\workspace\RuoYi-Vue-Plus\ruoyi-modules\ruoyi-bpo\src\main\java\org\dromara\bpo\controller\BpoCustomerController.java`
- **前端参考**: `D:\java\workspace\ruoyi-plus-vben5\apps\web-antd\src\views\system\user`

## 前端实现

### 1. API接口层 (api/bpo/customer/index.ts)

#### 新增API枚举

```typescript
enum Api {
  customerExport = '/bpo/customer/export',
  customerImport = '/bpo/customer/importData', // 新增
  customerImportTemplate = '/bpo/customer/importTemplate', // 新增
  customerList = '/bpo/customer/list',
  root = '/bpo/customer',
}
```

#### 新增API函数

```typescript
/**
 * 从excel导入客户
 * @param data 导入参数
 * @returns 导入结果
 */
export function importCustomer(data: CustomerImportParam) {
  return requestClient.post<{ code: number; msg: string }>(
    Api.customerImport,
    data,
    {
      headers: {
        'Content-Type': ContentTypeEnum.FORM_DATA,
      },
      isTransformResponse: false,
    },
  );
}

/**
 * 下载客户导入模板
 * @returns blob
 */
export function downloadCustomerImportTemplate() {
  return requestClient.post<Blob>(
    Api.customerImportTemplate,
    {},
    {
      isTransformResponse: false,
      responseType: 'blob',
    },
  );
}
```

### 2. 类型定义 (api/bpo/customer/model.d.ts)

```typescript
/**
 * 客户导入参数
 * @param updateSupport 是否覆盖数据
 * @param file excel文件
 */
export interface CustomerImportParam {
  updateSupport: boolean;
  file: Blob | File;
}
```

### 3. 导入模态框组件 (customer-import-modal.vue)

#### 主要功能

- **文件上传**: 支持拖拽上传，限制xlsx/xls格式
- **模板下载**: 提供导入模板下载功能
- **覆盖选项**: 可选择是否覆盖已存在的数据
- **结果提示**: 显示导入成功/失败的详细信息

#### 核心代码

```vue
<script setup lang="ts">
import {
  downloadCustomerImportTemplate,
  importCustomer,
} from '#/api/bpo/customer';

const fileList = ref<UploadFile[]>([]);
const checked = ref(false);

async function handleSubmit() {
  const data = {
    file: fileList.value[0]!.originFileObj as Blob,
    updateSupport: unref(checked),
  };
  const { code, msg } = await importCustomer(data);
  // 显示结果...
}
</script>

<template>
  <BasicModal title="客户导入">
    <UploadDragger
      v-model:file-list="fileList"
      :before-upload="() => false"
      accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
    />
    <!-- 模板下载和覆盖选项 -->
  </BasicModal>
</template>
```

### 4. 列表页面集成 (index.vue)

#### 添加导入按钮

```vue
<template #toolbar-tools>
  <Space>
    <a-button v-access:code="['bpo:customer:import']" @click="handleImport">
      {{ $t('pages.common.import') }}
    </a-button>
    <!-- 其他按钮... -->
  </Space>
</template>
```

#### 添加导入功能

```typescript
import { useVbenModal } from '@vben/common-ui';
import customerImportModal from './customer-import-modal.vue';

const [CustomerImportModal, customerImportModalApi] = useVbenModal({
  connectedComponent: customerImportModal,
});

function handleImport() {
  customerImportModalApi.open();
}
```

## 后端接口要求

### 1. 导入数据接口

**接口**: `POST /bpo/customer/importData` **参数**:

- `file`: MultipartFile (Excel文件)
- `updateSupport`: Boolean (是否覆盖已存在数据)

**响应格式**:

```json
{
  "code": 200,
  "msg": "导入成功！共导入 5 条数据，更新 2 条数据，失败 0 条数据。"
}
```

### 2. 导入模板接口

**接口**: `POST /bpo/customer/importTemplate` **响应**: Excel文件流 **用途**: 下载客户导入模板

### 3. 后端Controller示例

```java
@PostMapping("/importData")
@SaCheckPermission("bpo:customer:import")
public R<Void> importData(@RequestPart("file") MultipartFile file,
                         @RequestParam("updateSupport") Boolean updateSupport) throws Exception {
    String message = customerService.importCustomer(file, updateSupport);
    return R.ok(message);
}

@PostMapping("/importTemplate")
@SaCheckPermission("bpo:customer:import")
public void importTemplate(HttpServletResponse response) {
    ExcelUtil.exportExcel(new ArrayList<>(), "客户数据", "客户信息", BpoCustomerImportVo.class, response);
}
```

## Excel模板格式

### 模板列定义

| 列名     | 字段名         | 必填 | 说明                           |
| -------- | -------------- | ---- | ------------------------------ |
| 客户名称 | customerName   | 是   | 最大100字符                    |
| 所属部门 | departmentName | 否   | 部门名称，系统会自动匹配部门ID |
| 备注信息 | remark         | 否   | 备注说明                       |

### 导入规则

1. **客户名称**: 必填，作为唯一标识
2. **所属部门**: 可选，支持部门名称，系统自动转换为部门ID
3. **数据覆盖**: 根据客户名称判断是否为已存在数据
4. **错误处理**: 导入失败的数据会在结果中详细说明原因

## 权限控制

### 新增权限码

- `bpo:customer:import` - 导入客户权限

### 权限配置

需要在后端权限管理中添加该权限码，并分配给相应的角色。

## 功能特性

### 1. 用户体验

- ✅ 拖拽上传支持
- ✅ 文件格式验证
- ✅ 上传进度显示
- ✅ 详细的结果反馈

### 2. 数据处理

- ✅ 批量导入支持
- ✅ 数据覆盖选项
- ✅ 错误数据提示
- ✅ 部门名称自动匹配

### 3. 安全性

- ✅ 权限控制
- ✅ 文件类型限制
- ✅ 数据验证
- ✅ XSS防护

## 使用流程

### 1. 下载模板

1. 点击"导入"按钮
2. 在弹出的导入对话框中点击"下载模板"
3. 获得Excel模板文件

### 2. 填写数据

1. 在模板中填写客户信息
2. 确保必填字段完整
3. 部门名称填写正确的部门名称

### 3. 导入数据

1. 在导入对话框中上传填写好的Excel文件
2. 选择是否覆盖已存在的数据
3. 点击"确认"开始导入
4. 查看导入结果

## 错误处理

### 常见错误

1. **文件格式错误**: 只支持xlsx、xls格式
2. **必填字段缺失**: 客户名称不能为空
3. **部门不存在**: 填写的部门名称在系统中不存在
4. **数据重复**: 客户名称已存在且未选择覆盖

### 错误提示

系统会详细显示每条数据的导入结果，包括：

- 成功导入的数据条数
- 更新的数据条数
- 失败的数据条数及失败原因

## 测试验证

### 1. 功能测试

- [ ] 模板下载功能正常
- [ ] 文件上传功能正常
- [ ] 数据导入功能正常
- [ ] 覆盖选项功能正常
- [ ] 结果提示功能正常

### 2. 边界测试

- [ ] 空文件导入
- [ ] 大文件导入
- [ ] 错误格式文件
- [ ] 部分数据错误
- [ ] 全部数据错误

### 3. 权限测试

- [ ] 无权限用户无法看到导入按钮
- [ ] 无权限用户无法调用导入接口

## 总结

通过参考system/user模块的实现，成功为客户管理添加了完整的导入功能：

1. **前端实现**: 完整的API接口、类型定义、导入组件和页面集成
2. **用户体验**: 友好的拖拽上传界面和详细的结果反馈
3. **功能完整**: 支持模板下载、数据导入、覆盖选项等完整功能
4. **安全可靠**: 权限控制、数据验证、错误处理等安全措施

现在客户管理模块具备了完整的CRUD+导入导出功能，可以满足批量数据管理的需求。

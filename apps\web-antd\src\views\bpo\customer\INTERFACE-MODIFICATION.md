# 客户管理接口修改说明

## 修改目标

在客户列表中添加 `departmentName` 字段来显示部门名称，而不是只显示部门ID。

## 前端修改 (已完成)

### 1. 类型定义修改 (model.d.ts)

```typescript
export interface CustomerVo extends BaseEntity {
  id?: number;
  customerName?: string;
  departmentId?: string;
  departmentName?: string; // 新增：部门名称字段
  deptName?: string; // 兼容字段
  remark?: string;
  createTime?: string;
  updateTime?: string;
}
```

### 2. 列表配置修改 (data.ts)

```typescript
{
  title: '所属部门',
  field: 'departmentName',         // 修改：使用departmentName字段
  minWidth: 120,
  formatter({ cellValue }) {
    return cellValue || '暂无';
  },
}
```

### 3. 文档更新 (README.md)

- 更新了数据模型说明
- 添加了后端接口要求
- 明确了 `departmentName` 字段的必要性

## 后端修改要求

### 1. BpoCustomerVo 修改

在 `BpoCustomerVo.java` 中添加部门名称字段：

```java
public class BpoCustomerVo {
    /** 客户ID */
    private Long id;

    /** 客户名称 */
    private String customerName;

    /** 所属部门ID */
    private String departmentId;

    /** 部门名称 - 新增字段 */
    private String departmentName;

    /** 备注信息 */
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    // getter/setter 方法...
}
```

### 2. Mapper 查询修改

在 `BpoCustomerMapper.xml` 中修改查询SQL，关联部门表：

```xml
<!-- 查询客户信息列表 -->
<select id="selectVoList" parameterType="BpoCustomerBo" resultType="BpoCustomerVo">
    SELECT
        c.id,
        c.customer_name,
        c.department_id,
        c.remark,
        c.create_time,
        c.update_time,
        d.dept_name as department_name    -- 关联查询部门名称
    FROM bpo_customer c
    LEFT JOIN sys_dept d ON c.department_id = d.dept_id
    <where>
        <if test="customerName != null and customerName != ''">
            AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
        </if>
        <if test="departmentId != null and departmentId != ''">
            AND c.department_id = #{departmentId}
        </if>
        <!-- 其他查询条件 -->
    </where>
    ORDER BY c.create_time DESC
</select>
```

### 3. Service 层修改 (可选方案)

如果不想修改SQL，可以在Service层设置部门名称：

```java
@Override
public TableDataInfo<BpoCustomerVo> queryPageList(BpoCustomerBo bo, PageQuery pageQuery) {
    LambdaQueryWrapper<BpoCustomer> lqw = buildQueryWrapper(bo);
    Page<BpoCustomerVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

    // 设置部门名称
    result.getRecords().forEach(vo -> {
        if (StringUtils.isNotBlank(vo.getDepartmentId())) {
            SysDept dept = deptService.selectDeptById(Long.valueOf(vo.getDepartmentId()));
            if (dept != null) {
                vo.setDepartmentName(dept.getDeptName());
            }
        }
    });

    return TableDataInfo.build(result);
}
```

## 接口响应格式

### 客户列表接口 (GET /bpo/customer/list)

**修改前**:

```json
{
  "code": 200,
  "data": {
    "records": [
      {
        "id": 1,
        "customerName": "客户名称",
        "departmentId": "123",
        "remark": "备注信息"
      }
    ]
  }
}
```

**修改后**:

```json
{
  "code": 200,
  "data": {
    "records": [
      {
        "id": 1,
        "customerName": "客户名称",
        "departmentId": "123",
        "departmentName": "技术部", // 新增字段
        "remark": "备注信息",
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 1
  }
}
```

## 数据库表结构

### 客户表 (bpo_customer)

```sql
CREATE TABLE bpo_customer (
    id BIGINT PRIMARY KEY,
    customer_name VARCHAR(100) NOT NULL COMMENT '客户名称',
    department_id VARCHAR(20) COMMENT '所属部门ID',
    remark TEXT COMMENT '备注信息',
    create_time DATETIME COMMENT '创建时间',
    update_time DATETIME COMMENT '更新时间'
);
```

### 部门表 (sys_dept)

```sql
-- 系统已有的部门表
CREATE TABLE sys_dept (
    dept_id BIGINT PRIMARY KEY,
    dept_name VARCHAR(30) NOT NULL COMMENT '部门名称',
    parent_id BIGINT COMMENT '父部门ID',
    -- 其他字段...
);
```

## 测试验证

### 1. 数据库测试

```sql
-- 验证关联查询
SELECT
    c.id,
    c.customer_name,
    c.department_id,
    d.dept_name as department_name
FROM bpo_customer c
LEFT JOIN sys_dept d ON c.department_id = d.dept_id
LIMIT 10;
```

### 2. API测试

使用Postman或其他工具测试：

- **URL**: `GET /bpo/customer/list`
- **期望响应**: 包含 `departmentName` 字段
- **验证点**:
  - 有部门的客户显示部门名称
  - 无部门的客户 `departmentName` 为 null

### 3. 前端测试

1. 刷新客户列表页面
2. 检查所属部门列是否显示部门名称
3. 验证无部门的客户显示"暂无"

## 兼容性考虑

### 1. 向后兼容

- 保留 `departmentId` 字段用于编辑
- 新增 `departmentName` 字段用于显示
- 不影响现有的新增/编辑功能

### 2. 性能考虑

- 使用 LEFT JOIN 避免丢失无部门的客户
- 考虑添加索引优化查询性能：
  ```sql
  CREATE INDEX idx_customer_dept ON bpo_customer(department_id);
  ```

### 3. 数据一致性

- 确保部门ID的有效性
- 处理部门被删除的情况
- 考虑添加外键约束（可选）

## 实施步骤

### 阶段1: 后端修改

1. 修改 `BpoCustomerVo.java` 添加 `departmentName` 字段
2. 修改 `BpoCustomerMapper.xml` 添加关联查询
3. 测试API接口返回正确的数据格式

### 阶段2: 验证测试

1. 使用API测试工具验证接口响应
2. 检查数据库查询性能
3. 验证各种边界情况

### 阶段3: 前端验证

1. 刷新前端页面
2. 验证部门名称正确显示
3. 测试编辑功能正常工作

## 注意事项

1. **字段命名**: 使用 `departmentName` 而不是 `deptName`，保持命名一致性
2. **空值处理**: 无部门的客户 `departmentName` 应为 null，前端显示"暂无"
3. **性能优化**: 大量数据时考虑分页查询的性能影响
4. **数据同步**: 部门名称变更时客户列表会自动更新（因为是关联查询）

## 总结

通过添加 `departmentName` 字段，客户列表将能够直观地显示部门名称而不是部门ID，提升用户体验。前端代码已经完成修改，现在需要后端配合实现相应的接口修改。

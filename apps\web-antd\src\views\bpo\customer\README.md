# BPO Customer 前端代码生成总结

## 基于后端Controller生成的文件

根据 `D:\java\workspace\RuoYi-Vue-Plus\ruoyi-modules\ruoyi-bpo\src\main\java\org\dromara\bpo\controller\BpoCustomerController.java` 生成的前端代码。

## 后端API分析

### Controller接口

- `GET /bpo/customer/list` - 查询客户信息列表（分页）
- `POST /bpo/customer/export` - 导出客户信息列表
- `POST /bpo/customer/importData` - 导入客户信息
- `POST /bpo/customer/importTemplate` - 下载导入模板
- `GET /bpo/customer/{id}` - 获取客户信息详细信息
- `POST /bpo/customer` - 新增客户信息
- `PUT /bpo/customer` - 修改客户信息
- `DELETE /bpo/customer/{ids}` - 删除客户信息

### 数据模型

**BpoCustomerVo (视图对象):**

- `id` - 客户ID
- `customerName` - 客户名称
- `departmentId` - 所属部门ID
- `departmentName` - 部门名称（用于列表显示）
- `remark` - 备注信息
- `createTime` - 创建时间
- `updateTime` - 更新时间

**BpoCustomerBo (业务对象):**

- `id` - 客户ID（编辑时必填）
- `customerName` - 客户名称（必填，最大100字符）
- `departmentId` - 所属部门ID
- `remark` - 备注信息

## 生成的前端文件

### 1. API层

```
src/api/bpo/customer/
├── index.ts          # API接口定义
└── model.d.ts        # TypeScript类型定义
```

**API函数:**

- `getCustomerList()` - 获取客户列表
- `getCustomer()` - 获取客户详情
- `addCustomer()` - 新增客户
- `updateCustomer()` - 更新客户
- `delCustomer()` - 删除客户
- `exportCustomer()` - 导出客户
- `importCustomer()` - 导入客户
- `downloadCustomerImportTemplate()` - 下载导入模板

### 2. 视图层

```
src/views/bpo/customer/
├── index.vue                  # 主列表页面
├── customer-drawer.vue        # 编辑抽屉组件
├── customer-import-modal.vue  # 导入模态框组件
├── data.ts                   # 列表数据配置
├── drawer-data.ts            # 抽屉数据配置
└── README.md                 # 说明文档
```

### 3. 路由配置

- 更新了 `src/router/routes/modules/bpo.ts`
- 添加了客户管理路由：`/bpo/customer`

### 4. 国际化

- 更新了中英文国际化文件
- 添加了客户相关的翻译

## 功能特性

### 1. 列表页面 (index.vue)

- ✅ 分页查询
- ✅ 条件搜索（客户名称、所属部门）
- ✅ 批量删除
- ✅ 单行删除
- ✅ 导出功能
- ✅ 导入功能
- ✅ 新增/编辑操作
- ✅ 权限控制

### 2. 编辑抽屉 (customer-drawer.vue)

- ✅ 新增/编辑模式
- ✅ 表单验证
- ✅ 数据回显
- ✅ 保存操作
- ✅ 取消操作

### 3. 导入模态框 (customer-import-modal.vue)

- ✅ 文件上传（支持xlsx、xls格式）
- ✅ 模板下载
- ✅ 覆盖选项
- ✅ 导入结果提示

### 4. 数据配置

- ✅ 查询表单配置
- ✅ 表格列配置
- ✅ 编辑表单配置
- ✅ 表单验证规则

## 技术特点

### 1. vben5标准格式

- 使用 `useVbenVxeGrid` 构建表格
- 使用 `useVbenForm` 构建表单
- 使用 `useVbenDrawer` 构建抽屉
- 符合vben5的代码规范

### 2. TypeScript支持

- 完整的类型定义
- API接口类型安全
- 组件属性类型检查

### 3. 国际化支持

- 中英文双语支持
- 完整的翻译覆盖
- 动态语言切换

### 4. 权限控制

- 基于权限码的访问控制
- 按钮级别的权限控制
- 路由级别的权限控制

## 权限码

需要在后端配置以下权限码：

- `bpo:customer:list` - 查看客户列表
- `bpo:customer:query` - 查看客户详情
- `bpo:customer:add` - 新增客户
- `bpo:customer:edit` - 编辑客户
- `bpo:customer:remove` - 删除客户
- `bpo:customer:export` - 导出客户
- `bpo:customer:import` - 导入客户

## 使用说明

1. **访问路径**: `/bpo/customer`
2. **菜单位置**: BPO管理 > 客户管理
3. **功能操作**:
   - 查询：输入客户名称或选择部门进行搜索
   - 新增：点击"新增"按钮打开编辑抽屉
   - 编辑：点击行操作中的"编辑"按钮
   - 删除：支持单行删除和批量删除
   - 导出：导出当前查询条件下的数据
   - 导入：点击"导入"按钮上传Excel文件批量导入客户

## 后端接口要求

### 1. 客户列表接口 (GET /bpo/customer/list)

**响应格式**:

```json
{
  "code": 200,
  "data": {
    "records": [
      {
        "id": 1,
        "customerName": "客户名称",
        "departmentId": "123",
        "departmentName": "技术部",
        "remark": "备注信息",
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 1
  }
}
```

**重要**: 必须包含 `departmentName` 字段用于列表显示部门名称。

### 2. 客户详情接口 (GET /bpo/customer/{id})

**响应格式**:

```json
{
  "code": 200,
  "data": {
    "id": 1,
    "customerName": "客户名称",
    "departmentId": "123",
    "remark": "备注信息"
  }
}
```

### 3. 新增/更新接口 (POST/PUT /bpo/customer)

**请求格式**:

```json
{
  "customerName": "客户名称",
  "departmentId": "123",
  "remark": "备注信息"
}
```

### 4. 导入接口 (POST /bpo/customer/importData)

**请求格式**: multipart/form-data

- `file`: Excel文件 (xlsx/xls格式)
- `updateSupport`: boolean (是否覆盖已存在数据)

**响应格式**:

```json
{
  "code": 200,
  "msg": "导入成功！共导入 5 条数据，更新 2 条数据，失败 0 条数据。"
}
```

### 5. 导入模板接口 (POST /bpo/customer/importTemplate)

**响应**: Excel文件流 **用途**: 下载客户导入模板文件

## 注意事项

1. **部门选择**: 需要配置部门树数据源
2. **权限配置**: 确保后端已配置相应的权限码
3. **数据验证**: 客户名称为必填项，最大100字符
4. **部门显示**: 列表接口必须返回 `departmentName` 字段

## 扩展建议

1. **部门数据**: 可以集成系统部门管理，动态加载部门树
2. **客户分类**: 可以添加客户分类字段
3. **联系信息**: 可以添加客户联系人信息
4. **状态管理**: 可以添加客户状态（启用/禁用）
5. **关联项目**: 可以关联客户下的项目信息

<script setup lang="ts">
import type { CustomerBo } from '#/api/bpo/customer/model';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { addFullName, getPopupContainer } from '@vben/utils';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { addCustomer, getCustomer, updateCustomer } from '#/api/bpo/customer';
import { getDeptTree } from '#/api/system/user';
import { $t } from '#/locales';

import { drawerSchema } from './drawer-data';

defineOptions({ name: 'CustomerDrawer' });

const emit = defineEmits(['reload']);

const isUpdate = ref(false);

/**
 * 处理departmentId，转换为表单需要的格式
 * @param departmentId 部门ID，可能是字符串、数字或null/undefined
 * @returns 表单使用的部门ID或undefined
 */
function getDeptIdForForm(departmentId: any): number | undefined {
  console.log('处理departmentId:', {
    value: departmentId,
    type: typeof departmentId,
  });

  // 如果是null、undefined或空字符串，返回undefined
  if (!departmentId || departmentId === '') {
    console.log('departmentId为空，返回undefined');
    return undefined;
  }

  // 如果是数字类型
  if (typeof departmentId === 'number') {
    const result = departmentId > 0 ? departmentId : undefined;
    console.log('数字类型处理结果:', result);
    return result;
  }

  // 如果是字符串类型
  if (typeof departmentId === 'string') {
    const trimmed = departmentId.trim();
    if (trimmed === '' || trimmed === '0') {
      console.log('字符串为空或0，返回undefined');
      return undefined;
    }
    const num = Number(trimmed);
    const result = !isNaN(num) && num > 0 ? num : undefined;
    console.log('字符串类型处理结果:', { trimmed, num, result });
    return result;
  }

  // 其他类型，尝试转换为数字
  const num = Number(departmentId);
  const result = !isNaN(num) && num > 0 ? num : undefined;
  console.log('其他类型处理结果:', { num, result });
  return result;
}

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  schema: drawerSchema(),
  showDefaultActions: false,
});

/**
 * 初始化部门选择
 */
async function setupDeptSelect() {
  const deptTree = await getDeptTree();
  console.log('获取到的部门树数据:', deptTree);

  // 选中后显示在输入框的值 即父节点 / 子节点
  addFullName(deptTree, 'label', ' / ');

  formApi.updateSchema([
    {
      componentProps: {
        class: 'w-full',
        fieldNames: {
          key: 'id',
          value: 'id',
          children: 'children',
        },
        getPopupContainer,
        placeholder: '请选择部门',
        showSearch: true,
        treeData: deptTree,
        treeDefaultExpandAll: true,
        treeLine: { showLeafIcon: false },
        // 筛选的字段
        treeNodeFilterProp: 'label',
        // 选中后显示在输入框的值
        treeNodeLabelProp: 'fullName',
        // 确保没有默认值
        allowClear: true,
        value: undefined,
        defaultValue: undefined,
      },
      fieldName: 'deptId',
      // 确保字段没有默认值
      defaultValue: undefined,
    },
  ]);
}

async function handleSubmit() {
  try {
    console.log('开始表单验证...');

    // 先获取当前表单值
    const currentValues = await formApi.getValues();
    console.log('当前表单值:', currentValues);

    // 然后进行验证
    let validationResult;
    try {
      validationResult = await formApi.validate();
      console.log('表单验证通过，验证结果:', validationResult);
    } catch (error) {
      console.error('表单验证失败:', error);
      throw error;
    }

    // 使用当前表单值作为最终值
    const finalValues = currentValues;
    console.log('最终使用的表单数据:', finalValues);
    console.log('表单字段详情:');
    console.log('- customerName:', finalValues?.customerName);
    console.log('- deptId:', finalValues?.deptId);
    console.log('- remark:', finalValues?.remark);
    console.log('- id:', finalValues?.id);

    // 设置抽屉loading状态
    drawerApi.setState({ confirmLoading: true });

    // 构建符合后端BpoCustomerBo要求的数据对象
    const customerBo: CustomerBo = {};

    // 处理必填字段：客户名称
    if (finalValues.customerName && finalValues.customerName.trim() !== '') {
      customerBo.customerName = finalValues.customerName.trim();
    } else {
      throw new Error('客户名称不能为空');
    }

    // 处理可选字段：备注信息
    if (finalValues.remark && finalValues.remark.trim() !== '') {
      customerBo.remark = finalValues.remark.trim();
    }

    // 处理部门ID：只有选择了部门才添加该字段
    if (finalValues.deptId) {
      customerBo.departmentId = String(finalValues.deptId);
    }

    // 编辑模式需要添加ID
    if (isUpdate.value) {
      const data = drawerApi.getData();
      customerBo.id = data.id;
      console.log('编辑模式，客户ID:', customerBo.id);
    }

    console.log('构建的客户数据对象:', customerBo);
    console.log('JSON字符串:', JSON.stringify(customerBo, null, 2));

    console.log('准备提交的客户数据:', customerBo);

    if (isUpdate.value) {
      console.log('更新客户，ID:', customerBo.id);
      await updateCustomer(customerBo);
    } else {
      console.log('新增客户');
      await addCustomer(customerBo);
    }

    drawerApi.close();
    emit('reload');
    message.success($t('bpo.message.saveSuccess'));
  } catch (error) {
    console.error('保存客户信息失败:', error);
    message.error(`保存失败: ${error?.message || '未知错误'}`);
  } finally {
    // 重置loading状态
    drawerApi.setState({ confirmLoading: false });
  }
}

const [DrawerComponent, drawerApi] = useVbenDrawer({
  onConfirm: handleSubmit,
  onCancel: () => {
    drawerApi.close();
  },
  onOpenChange: async (isOpen: boolean) => {
    if (!isOpen) return;

    // 设置抽屉loading状态
    drawerApi.setState({ loading: true });
    const data = drawerApi.getData();
    isUpdate.value = !!data?.id;

    console.log('抽屉打开，数据:', data, '是否更新模式:', isUpdate.value);

    try {
      // 先初始化部门选择（在设置数据之前）
      await setupDeptSelect();

      if (isUpdate.value && data?.id) {
        console.log('加载客户数据，ID:', data.id);
        const customer = await getCustomer(data.id);
        console.log('获取到的客户数据:', customer);
        console.log('departmentId详细信息:', {
          value: customer.departmentId,
          type: typeof customer.departmentId,
          isNull: customer.departmentId === null,
          isUndefined: customer.departmentId === undefined,
          isEmpty: customer.departmentId === '',
        });

        // 构建表单数据，只包含表单需要的字段
        const formData = {
          id: customer.id,
          customerName: customer.customerName,
          // 处理departmentId，支持多种数据类型
          deptId: getDeptIdForForm(customer.departmentId),
          remark: customer.remark,
        };
        console.log('设置表单数据:', formData);
        console.log('deptId处理结果:', {
          原始值: customer.departmentId,
          处理后: formData.deptId,
          是否为undefined: formData.deptId === undefined,
        });

        await formApi.setValues(formData);

        // 验证表单值是否正确设置
        const setValues = await formApi.getValues();
        console.log('设置后的表单值:', setValues);
      } else {
        console.log('新增模式，重置表单');

        // 重置表单
        await formApi.resetForm();

        // 验证重置后的表单值
        const resetValues = await formApi.getValues();
        console.log('重置后的表单值:', resetValues);
      }
    } catch (error) {
      console.error('初始化抽屉失败:', error);
      message.error(`初始化失败: ${error?.message || '未知错误'}`);
    } finally {
      // 重置loading状态
      drawerApi.setState({ loading: false });
    }
  },
});

const getTitle = computed(() =>
  isUpdate.value ? $t('bpo.title.editCustomer') : $t('bpo.title.addCustomer'),
);
</script>

<template>
  <DrawerComponent :title="getTitle" width="600px">
    <div class="p-4">
      <BasicForm />
    </div>
  </DrawerComponent>
</template>

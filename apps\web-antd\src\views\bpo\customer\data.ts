import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'customerName',
    label: '客户名称',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '客户ID',
    field: 'id',
    width: 80,
  },
  {
    title: '客户名称',
    field: 'customerName',
    width: 200,
  },
  {
    title: '所属部门',
    field: 'departmentName',
    minWidth: 120,
    formatter({ cellValue }) {
      return cellValue || '暂无';
    },
  },
  {
    title: '备注信息',
    field: 'remark',
    width: 300,
  },
  {
    title: '创建时间',
    field: 'createTime',
    width: 180,
  },
  {
    title: '更新时间',
    field: 'updateTime',
    width: 180,
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    resizable: false,
    width: 'auto',
  },
];

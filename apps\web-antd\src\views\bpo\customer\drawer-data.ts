import type { FormSchemaGetter } from '#/adapter/form';

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'id',
    label: 'id',
  },
  {
    component: 'Input',
    fieldName: 'customerName',
    label: '客户名称',
    rules: 'required',
    componentProps: {
      placeholder: '请输入客户名称',
    },
  },
  {
    component: 'TreeSelect',
    fieldName: 'deptId',
    label: '所属部门',
    componentProps: {
      placeholder: '请选择部门',
      allowClear: true,
    },
  },
  {
    component: 'Textarea',
    fieldName: 'remark',
    formItemClass: 'items-start',
    label: '备注信息',
    componentProps: {
      placeholder: '请输入备注信息',
      rows: 3,
    },
  },
];

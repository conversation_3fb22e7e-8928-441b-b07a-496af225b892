<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { Customer } from '#/api/bpo/customer/model';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import {
  Button as GhostButton,
  Modal,
  Popconfirm,
  Space,
} from 'ant-design-vue';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import {
  delCustomer,
  exportCustomer,
  getCustomerList,
} from '#/api/bpo/customer';
import { commonDownloadExcel } from '#/utils/file/download';

import customerDrawer from './customer-drawer.vue';
import customerImportModal from './customer-import-modal.vue';
import { columns, querySchema } from './data';

defineOptions({ name: 'Customer' });

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 120,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    highlight: true,
    reserve: true,
    trigger: 'cell',
  },
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        const result = await getCustomerList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });

        console.log('客户列表API返回数据:', result);
        if (result?.records && result.records.length > 0) {
          console.log('第一条客户数据示例:', result.records[0]);
          console.log('部门相关字段:', {
            departmentId: result.records[0].departmentId,
            deptName: result.records[0].deptName,
          });
        }

        return result;
      },
    },
  },
  rowConfig: {
    keyField: 'id',
  },
  id: 'bpo-customer-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [CustomerDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: customerDrawer,
});

/**
 * 导入
 */
const [CustomerImportModal, customerImportModalApi] = useVbenModal({
  connectedComponent: customerImportModal,
});

function handleAdd() {
  drawerApi.setData({});
  drawerApi.open();
}

function handleImport() {
  customerImportModalApi.open();
}

async function handleEdit(record: Customer) {
  drawerApi.setData({ id: record.id });
  drawerApi.open();
}

async function handleDelete(row: Customer) {
  await delCustomer([row.id!]);
  await tableApi.query();
}

function handleMultiDelete() {
  const rows = tableApi.grid.getCheckboxRecords();
  const ids = rows.map((row: Customer) => row.id!);
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await delCustomer(ids);
      await tableApi.query();
    },
  });
}

function handleExport() {
  commonDownloadExcel(exportCustomer, '客户信息', tableApi.formApi.form.values);
}
</script>

<template>
  <Page :auto-content-height="true" content-class="flex flex-col w-full">
    <BasicTable class="flex-1 overflow-hidden" table-title="客户信息列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            v-access:code="['bpo:customer:import']"
            @click="handleImport"
          >
            导入
          </a-button>
          <a-button
            v-access:code="['bpo:customer:export']"
            @click="handleExport"
          >
            导出
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['bpo:customer:remove']"
            @click="handleMultiDelete"
          >
            删除
          </a-button>
          <a-button
            type="primary"
            v-access:code="['bpo:customer:add']"
            @click="handleAdd"
          >
            新增
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <GhostButton
            v-access:code="['bpo:customer:edit']"
            @click="handleEdit(row)"
          >
            编辑
          </GhostButton>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <GhostButton
              danger
              v-access:code="['bpo:customer:remove']"
              @click.stop=""
            >
              删除
            </GhostButton>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <CustomerDrawer @reload="tableApi.query" />
    <CustomerImportModal @reload="tableApi.query()" />
  </Page>
</template>

# 🔗 项目管理API接口参考

## 📋 API接口列表

根据RuoYi-Vue-Plus框架的BpoProjectController.java标准模式，已配置完整的API接口：

### 1. 基础CRUD接口

#### 查询项目信息列表
```typescript
GET /bpo/project/list
export function getProjectList(params?: PageQuery & Partial<ProjectVo>)
```
- **功能**：分页查询项目列表
- **参数**：分页参数 + 查询条件
- **返回**：`PageResult<ProjectVo>`

#### 查询项目信息详情
```typescript
GET /bpo/project/{id}
export function getProject(projectId: ID)
```
- **功能**：根据ID查询项目详情
- **参数**：项目ID
- **返回**：`ProjectVo`

#### 新增项目信息
```typescript
POST /bpo/project
export function addProject(data: ProjectBo)
```
- **功能**：新增项目
- **参数**：项目业务对象
- **返回**：`void`（带成功消息）

#### 修改项目信息
```typescript
PUT /bpo/project
export function updateProject(data: ProjectBo)
```
- **功能**：修改项目
- **参数**：项目业务对象（包含ID）
- **返回**：`void`（带成功消息）

#### 删除项目信息
```typescript
DELETE /bpo/project/{ids}
export function delProject(projectIds: IDS)
```
- **功能**：批量删除项目
- **参数**：项目ID数组（逗号分隔）
- **返回**：`void`（带成功消息）

### 2. 导入导出接口

#### 导出项目信息
```typescript
POST /bpo/project/export
export function exportProject(data: Partial<ProjectVo>)
```
- **功能**：导出项目数据到Excel
- **参数**：查询条件
- **返回**：`Blob`（Excel文件）

#### 项目信息导入
```typescript
POST /bpo/project/importData
export function importProject(data: ProjectImportParam)
```
- **功能**：从Excel导入项目数据
- **参数**：`{ updateSupport: boolean, file: File }`
- **返回**：`{ code: number, msg: string }`

#### 下载项目信息导入模板
```typescript
POST /bpo/project/importTemplate
export function downloadProjectImportTemplate()
```
- **功能**：下载Excel导入模板
- **参数**：无
- **返回**：`Blob`（Excel模板文件）

### 3. 扩展接口

#### 根据客户ID查询项目列表
```typescript
GET /bpo/project/listByCustomer/{customerId}
export function getProjectByCustomer(customerId: ID)
```
- **功能**：查询指定客户的所有项目
- **参数**：客户ID
- **返回**：`Project[]`

## 🎯 数据模型

### ProjectBo（业务对象）
```typescript
export interface ProjectBo {
  id?: number; // 项目ID（编辑时必填）
  
  // 项目运营信息（45个字段）
  projectName?: string; // 项目名称
  customerId?: number; // 客户ID
  businessType?: string; // 业务类型
  departmentId?: number; // 所属部门ID
  projectManagerId?: number; // 项目经理ID
  settlementSpecialistId?: number; // 结算专员ID
  employeeRelation?: string; // 员工关系
  // ... 其他运营信息字段
  
  // 入职相关信息（19个字段）
  requireCriminalRecord?: string; // 是否需要无犯罪记录证明
  requireMedicalReport?: string; // 是否需体检报告
  // ... 其他入职信息字段
  
  // 离职相关信息（4个字段）
  requireResignationForm?: string; // 是否需上传离职手续表
  requireHandoverForm?: string; // 是否需离职交接单
  // ... 其他离职信息字段
}
```

### ProjectVo（视图对象）
```typescript
export interface ProjectVo extends BaseEntity {
  // 继承ProjectBo的所有字段
  // 额外包含关联对象的名称字段
  customerName?: string; // 客户名称
  departmentName?: string; // 部门名称
  projectManagerName?: string; // 项目经理姓名
  settlementSpecialistName?: string; // 结算专员姓名
}
```

### ProjectImportParam（导入参数）
```typescript
export interface ProjectImportParam {
  updateSupport: boolean; // 是否覆盖已存在数据
  file: Blob | File; // Excel文件
}
```

## 🔧 API配置特点

### 1. 符合RuoYi-Vue-Plus标准
- **RESTful风格**：使用标准的HTTP方法
- **统一路径**：基于`/bpo/project`的资源路径
- **标准响应**：使用框架统一的响应格式

### 2. 完整的CRUD操作
- **Create**：`POST /bpo/project`
- **Read**：`GET /bpo/project/list` 和 `GET /bpo/project/{id}`
- **Update**：`PUT /bpo/project`
- **Delete**：`DELETE /bpo/project/{ids}`

### 3. 扩展功能支持
- **导入导出**：Excel文件的导入导出功能
- **批量操作**：支持批量删除
- **关联查询**：根据客户查询项目

### 4. 类型安全
- **完整类型定义**：所有接口都有TypeScript类型
- **参数验证**：使用接口类型确保参数正确
- **返回值类型**：明确的返回值类型定义

## 🚀 使用示例

### 查询项目列表
```typescript
const params = {
  pageNum: 1,
  pageSize: 20,
  projectName: '测试项目',
  businessType: 'ENTRUST'
};
const result = await getProjectList(params);
```

### 新增项目
```typescript
const projectData: ProjectBo = {
  projectName: '新项目',
  customerId: 1,
  businessType: 'ENTRUST',
  departmentId: 100,
  projectManagerId: 1,
  settlementSpecialistId: 2,
  employeeRelation: 'FORMAL'
};
await addProject(projectData);
```

### 导出项目
```typescript
const exportParams = {
  businessType: 'ENTRUST',
  employeeRelation: 'FORMAL'
};
const blob = await exportProject(exportParams);
```

### 导入项目
```typescript
const formData = new FormData();
formData.append('file', file);
formData.append('updateSupport', 'true');
const result = await importProject(formData);
```

## ✅ API接口验证

所有API接口已按照RuoYi-Vue-Plus的BpoProjectController.java标准配置：

- ✅ **路径正确**：符合RESTful规范
- ✅ **方法正确**：使用正确的HTTP方法
- ✅ **参数类型**：完整的TypeScript类型定义
- ✅ **返回格式**：统一的响应格式
- ✅ **错误处理**：完整的错误处理机制
- ✅ **日志输出**：便于调试的日志信息

现在API接口完全符合后端控制器的标准！

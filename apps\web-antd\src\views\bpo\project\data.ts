import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridPropTypes } from '#/adapter/vxe-table';

// 列表查询表单配置
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'projectName',
    label: '项目名称',
    componentProps: {
      placeholder: '请输入项目名称',
    },
  },
  {
    component: 'Select',
    fieldName: 'businessType',
    label: '业务类型',
    componentProps: {
      placeholder: '请选择业务类型',
      allowClear: true,
      options: [
        { label: '委托', value: 'ENTRUST' },
        { label: '派遣', value: 'DISPATCH' },
        { label: '形式外包', value: 'FORMAL_OUTSOURCING' },
        { label: '岗位外包', value: 'POSITION_OUTSOURCING' },
        { label: '灵活用工', value: 'FLEXIBLE_EMPLOYMENT' },
        { label: '背调', value: 'BACKGROUND_CHECK' },
        { label: '商保', value: 'COMMERCIAL_INSURANCE' },
      ],
    },
  },
  {
    component: 'Select',
    fieldName: 'employeeRelation',
    label: '员工关系',
    componentProps: {
      placeholder: '请选择员工关系',
      allowClear: true,
      options: [
        { label: '正式员工', value: 'FORMAL' },
        { label: '派遣员工', value: 'DISPATCH' },
        { label: '外包员工', value: 'OUTSOURCE' },
        { label: '临时员工', value: 'TEMPORARY' },
      ],
    },
  },
  {
    component: 'Select',
    fieldName: 'customerId',
    label: '客户名称',
    componentProps: {
      placeholder: '请选择客户',
      allowClear: true,
      // 这里需要动态加载客户列表
    },
  },
];

// 列表列配置
export const columns: VxeGridPropTypes.Columns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
  },
  {
    title: '序号',
    type: 'seq',
    width: 60,
    fixed: 'left',
  },
  {
    title: '项目名称',
    field: 'projectName',
    width: 200,
    fixed: 'left',
    showOverflow: 'tooltip',
  },
  {
    title: '项目在职人数',
    field: 'projectStaffCount',
    width: 120,
    align: 'center',
    formatter: ({ cellValue }) => {
      return cellValue ? `${cellValue}人` : '-';
    },
  },
  {
    title: '业务类型',
    field: 'businessType',
    width: 120,
    align: 'center',
    formatter: ({ cellValue }) => {
      const typeMap: Record<string, string> = {
        ENTRUST: '委托',
        DISPATCH: '派遣',
        FORMAL_OUTSOURCING: '形式外包',
        POSITION_OUTSOURCING: '岗位外包',
        FLEXIBLE_EMPLOYMENT: '灵活用工',
        BACKGROUND_CHECK: '背调',
        COMMERCIAL_INSURANCE: '商保',
      };
      return typeMap[cellValue] || cellValue || '-';
    },
  },
  {
    title: '员工关系',
    field: 'employeeRelation',
    width: 120,
    align: 'center',
    formatter: ({ cellValue }) => {
      const relationMap: Record<string, string> = {
        FORMAL: '正式员工',
        DISPATCH: '派遣员工',
        OUTSOURCE: '外包员工',
        TEMPORARY: '临时员工',
      };
      return relationMap[cellValue] || cellValue || '-';
    },
  },
  {
    title: '项目经理',
    field: 'projectManagerName',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '结算专员',
    field: 'settlementSpecialistName',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '所属部门',
    field: 'departmentName',
    width: 150,
    showOverflow: 'tooltip',
  },
  {
    title: '客户名称',
    field: 'customerName',
    width: 150,
    showOverflow: 'tooltip',
  },
  {
    title: '客户对接人',
    field: 'customerContact',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '对接人联系方式',
    field: 'customerContactPhone',
    width: 140,
    showOverflow: 'tooltip',
  },
  {
    title: '商务合同开始时间',
    field: 'contractStartDate',
    width: 140,
    align: 'center',
    formatter: ({ cellValue }) => {
      return cellValue || '-';
    },
  },
  {
    title: '商务合同到期时间',
    field: 'contractEndDate',
    width: 140,
    align: 'center',
    formatter: ({ cellValue }) => {
      return cellValue || '-';
    },
  },
  {
    title: '创建时间',
    field: 'createTime',
    width: 160,
    align: 'center',
    formatter: ({ cellValue }) => {
      return cellValue || '-';
    },
  },
  {
    title: '操作',
    field: 'action',
    width: 160,
    fixed: 'right',
    slots: { default: 'action' },
  },
];

import type { FormSchemaGetter } from '#/adapter/form';

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'id',
    label: 'id',
  },

  // 项目运营信息分组
  {
    component: 'Divider',
    fieldName: 'projectOperationDivider',
    label: '项目运营信息',
    formItemClass: 'col-span-2',
    componentProps: {
      orientation: 'left',
    },
  },

  // 第一行：客户名称、业务类型
  {
    component: 'Select',
    fieldName: 'customerId',
    label: '客户名称',
    rules: 'required',
    componentProps: {
      placeholder: '请选择客户',
      allowClear: true,
    },
  },
  {
    component: 'Select',
    fieldName: 'businessType',
    label: '业务类型',
    rules: 'required',
    componentProps: {
      placeholder: '请选择业务类型',
      allowClear: true,
      options: [
        { label: '委托', value: 'ENTRUST' },
        { label: '派遣', value: 'DISPATCH' },
        { label: '形式外包', value: 'FORMAL_OUTSOURCING' },
        { label: '岗位外包', value: 'POSITION_OUTSOURCING' },
        { label: '灵活用工', value: 'FLEXIBLE_EMPLOYMENT' },
        { label: '背调', value: 'BACKGROUND_CHECK' },
        { label: '商保', value: 'COMMERCIAL_INSURANCE' },
      ],
    },
  },

  // 第二行：项目名称、所属部门
  {
    component: 'Input',
    fieldName: 'projectName',
    label: '项目名称',
    rules: 'required',
    componentProps: {
      placeholder: '请输入项目名称',
      maxlength: 100,
    },
  },
  {
    component: 'TreeSelect',
    fieldName: 'departmentId',
    label: '所属部门',
    rules: 'required',
    componentProps: {
      placeholder: '请选择部门',
      allowClear: true,
      style: { width: '100%' },
    },
  },

  // 第三行：客户对接人、对接人联系方式
  {
    component: 'Input',
    fieldName: 'customerContact',
    label: '客户对接人',
    componentProps: {
      placeholder: '请输入客户对接人',
      maxlength: 50,
    },
  },
  {
    component: 'Input',
    fieldName: 'customerContactPhone',
    label: '对接人联系方式',
    componentProps: {
      placeholder: '请输入联系方式',
      maxlength: 20,
    },
  },

  // 第四行：项目在职人数、项目经理
  {
    component: 'InputNumber',
    fieldName: 'projectStaffCount',
    label: '项目在职人数',
    componentProps: {
      placeholder: '请输入在职人数',
      min: 0,
      precision: 0,
      style: { width: '100%' },
    },
  },
  {
    component: 'Select',
    fieldName: 'projectManagerId',
    label: '项目经理',
    rules: 'required',
    componentProps: {
      placeholder: '请选择项目经理',
      allowClear: true,
    },
  },

  // 第五行：结算专员、员工关系
  {
    component: 'Select',
    fieldName: 'settlementSpecialistId',
    label: '结算专员',
    rules: 'required',
    componentProps: {
      placeholder: '请选择结算专员',
      allowClear: true,
    },
  },
  {
    component: 'Select',
    fieldName: 'employeeRelation',
    label: '员工关系',
    rules: 'required',
    componentProps: {
      placeholder: '请选择员工关系',
      allowClear: true,
      options: [
        { label: '正式员工', value: 'FORMAL' },
        { label: '派遣员工', value: 'DISPATCH' },
        { label: '外包员工', value: 'OUTSOURCE' },
        { label: '临时员工', value: 'TEMPORARY' },
      ],
    },
  },

  // 招聘需求（占满整行）
  {
    component: 'Textarea',
    fieldName: 'recruitmentRequirement',
    label: '招聘需求',
    formItemClass: 'col-span-2',
    componentProps: {
      placeholder: '请输入招聘需求',
      rows: 3,
      maxlength: 500,
      showCount: true,
    },
  },

  // 第六行：是否要体检、是否要背调
  {
    component: 'Select',
    fieldName: 'requireMedicalExam',
    label: '是否要体检',
    componentProps: {
      placeholder: '请选择是否要体检',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },
  {
    component: 'Select',
    fieldName: 'requireBackgroundCheck',
    label: '是否要背调',
    componentProps: {
      placeholder: '请选择是否要背调',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },

  // 第七行：试用期时间、13薪发放时间
  {
    component: 'Input',
    fieldName: 'probationPeriodTime',
    label: '试用期时间',
    componentProps: {
      placeholder: '请输入试用期时间',
      maxlength: 50,
    },
  },
  {
    component: 'Input',
    fieldName: 'thirteenthSalaryTime',
    label: '13薪发放时间',
    componentProps: {
      placeholder: '请输入13薪发放时间',
      maxlength: 50,
    },
  },

  // 第八行：年终奖发放规则、年终奖发放时间
  {
    component: 'Input',
    fieldName: 'yearEndBonusRule',
    label: '年终奖发放规则',
    componentProps: {
      placeholder: '请输入年终奖发放规则',
      maxlength: 100,
    },
  },
  {
    component: 'Input',
    fieldName: 'yearEndBonusTime',
    label: '年终奖发放时间',
    componentProps: {
      placeholder: '请输入年终奖发放时间',
      maxlength: 50,
    },
  },

  // 第九行：端午中秋等节假日福利、高温补贴
  {
    component: 'Input',
    fieldName: 'holidayBenefit',
    label: '端午中秋等节假日福利',
    componentProps: {
      placeholder: '请输入节假日福利',
      maxlength: 100,
    },
  },
  {
    component: 'Input',
    fieldName: 'highTemperatureAllowance',
    label: '高温补贴',
    componentProps: {
      placeholder: '请输入高温补贴',
      maxlength: 50,
    },
  },

  // 第十行：假期规则、病假规则
  {
    component: 'Input',
    fieldName: 'vacationRule',
    label: '假期规则',
    componentProps: {
      placeholder: '请输入假期规则',
      maxlength: 100,
    },
  },
  {
    component: 'Input',
    fieldName: 'sickLeaveRule',
    label: '病假规则',
    componentProps: {
      placeholder: '请输入病假规则',
      maxlength: 100,
    },
  },

  // 第十一行：年假规则、账单日
  {
    component: 'Input',
    fieldName: 'annualLeaveRule',
    label: '年假规则',
    componentProps: {
      placeholder: '请输入年假规则',
      maxlength: 100,
    },
  },
  {
    component: 'Input',
    fieldName: 'billingDate',
    label: '账单日',
    componentProps: {
      placeholder: '请输入账单日',
      maxlength: 20,
    },
  },

  // 第十二行：账单确认流程、发薪日
  {
    component: 'Input',
    fieldName: 'billConfirmProcess',
    label: '账单确认流程',
    componentProps: {
      placeholder: '请输入账单确认流程',
      maxlength: 100,
    },
  },
  {
    component: 'Input',
    fieldName: 'payrollDate',
    label: '发薪日',
    componentProps: {
      placeholder: '请输入发薪日',
      maxlength: 20,
    },
  },

  // 第十三行：发薪流程、账期
  {
    component: 'Input',
    fieldName: 'payrollProcess',
    label: '发薪流程',
    componentProps: {
      placeholder: '请输入发薪流程',
      maxlength: 100,
    },
  },
  {
    component: 'Input',
    fieldName: 'paymentTerm',
    label: '账期',
    componentProps: {
      placeholder: '请输入账期',
      maxlength: 50,
    },
  },

  // 第十四行：开票流程、结算流程
  {
    component: 'Input',
    fieldName: 'invoiceProcess',
    label: '开票流程',
    componentProps: {
      placeholder: '请输入开票流程',
      maxlength: 100,
    },
  },
  {
    component: 'Input',
    fieldName: 'settlementProcess',
    label: '结算流程',
    componentProps: {
      placeholder: '请输入结算流程',
      maxlength: 100,
    },
  },

  // 第十五行：商务合同开始时间、商务合同到期时间
  {
    component: 'DatePicker',
    fieldName: 'contractStartDate',
    label: '商务合同开始时间',
    componentProps: {
      placeholder: '请选择商务合同开始时间',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
  },
  {
    component: 'DatePicker',
    fieldName: 'contractEndDate',
    label: '商务合同到期时间',
    componentProps: {
      placeholder: '请选择商务合同到期时间',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
  },

  // 仝力系统访问链接（占满整行）
  {
    component: 'Input',
    fieldName: 'systemAccessLink',
    label: '仝力系统访问链接',
    formItemClass: 'col-span-2',
    componentProps: {
      placeholder: '请输入仝力系统访问链接',
      maxlength: 200,
    },
  },

  // 入职相关信息分组
  {
    component: 'Divider',
    fieldName: 'onboardingInfoDivider',
    label: '入职相关信息',
    formItemClass: 'col-span-2',
    componentProps: {
      orientation: 'left',
    },
  },

  // 第一行：是否需要无犯罪记录证明、是否需体检报告
  {
    component: 'Select',
    fieldName: 'requireCriminalRecord',
    label: '是否需要无犯罪记录证明',
    componentProps: {
      placeholder: '请选择是否需要无犯罪记录证明',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },
  {
    component: 'Select',
    fieldName: 'requireMedicalReport',
    label: '是否需体检报告',
    componentProps: {
      placeholder: '请选择是否需体检报告',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },

  // 第二行：是否需要上家公司离职证明、是否需电子一寸照
  {
    component: 'Select',
    fieldName: 'requireResignationCert',
    label: '是否需要上家公司离职证明',
    componentProps: {
      placeholder: '请选择是否需要上家公司离职证明',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },
  {
    component: 'Select',
    fieldName: 'requireElectronicPhoto',
    label: '是否需电子一寸照',
    componentProps: {
      placeholder: '请选择是否需电子一寸照',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },

  // 第三行：是否需职业证书、现住地址是否必填
  {
    component: 'Select',
    fieldName: 'requireProfessionalCert',
    label: '是否需职业证书',
    componentProps: {
      placeholder: '请选择是否需职业证书',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },
  {
    component: 'Select',
    fieldName: 'currentAddressRequired',
    label: '现住地址是否必填',
    componentProps: {
      placeholder: '请选择现住地址是否必填',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },

  // 第四行：是否需要学历证书、是否需要学位证书
  {
    component: 'Select',
    fieldName: 'requireEducationCert',
    label: '是否需要学历证书',
    componentProps: {
      placeholder: '请选择是否需要学历证书',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },
  {
    component: 'Select',
    fieldName: 'requireDegreeCert',
    label: '是否需要学位证书',
    componentProps: {
      placeholder: '请选择是否需要学位证书',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },

  // 第五行：是否需要户口本、是否需要阳光申报
  {
    component: 'Select',
    fieldName: 'requireHouseholdRegister',
    label: '是否需要户口本',
    componentProps: {
      placeholder: '请选择是否需要户口本',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },
  {
    component: 'Select',
    fieldName: 'requireSunshineDeclaration',
    label: '是否需要阳光申报',
    componentProps: {
      placeholder: '请选择是否需要阳光申报',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },

  // 第六行：民族是否必填、婚姻状况是否必填
  {
    component: 'Select',
    fieldName: 'ethnicityRequired',
    label: '民族是否必填',
    componentProps: {
      placeholder: '请选择民族是否必填',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },
  {
    component: 'Select',
    fieldName: 'maritalStatusRequired',
    label: '婚姻状况是否必填',
    componentProps: {
      placeholder: '请选择婚姻状况是否必填',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },

  // 第七行：政治面貌是否必填、籍贯是否必填
  {
    component: 'Select',
    fieldName: 'politicalStatusRequired',
    label: '政治面貌是否必填',
    componentProps: {
      placeholder: '请选择政治面貌是否必填',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },
  {
    component: 'Select',
    fieldName: 'nativePlaceRequired',
    label: '籍贯是否必填',
    componentProps: {
      placeholder: '请选择籍贯是否必填',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },

  // 第八行：户籍性质是否必填、身高是否必填
  {
    component: 'Select',
    fieldName: 'householdTypeRequired',
    label: '户籍性质是否必填',
    componentProps: {
      placeholder: '请选择户籍性质是否必填',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },
  {
    component: 'Select',
    fieldName: 'heightRequired',
    label: '身高是否必填',
    componentProps: {
      placeholder: '请选择身高是否必填',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },

  // 第九行：体重是否必填、是否需要员工应聘登记表
  {
    component: 'Select',
    fieldName: 'weightRequired',
    label: '体重是否必填',
    componentProps: {
      placeholder: '请选择体重是否必填',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },
  {
    component: 'Select',
    fieldName: 'requireEmployeeRegistrationForm',
    label: '是否需要员工应聘登记表',
    componentProps: {
      placeholder: '请选择是否需要员工应聘登记表',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },

  // 是否需要银行卡照片（占一列）
  {
    component: 'Select',
    fieldName: 'requireBankCardPhoto',
    label: '是否需要银行卡照片',
    componentProps: {
      placeholder: '请选择是否需要银行卡照片',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },

  // 离职相关信息分组
  {
    component: 'Divider',
    fieldName: 'resignationInfoDivider',
    label: '离职相关信息',
    formItemClass: 'col-span-2',
    componentProps: {
      orientation: 'left',
    },
  },

  // 第一行：是否需上传离职手续表、是否需离职交接单
  {
    component: 'Select',
    fieldName: 'requireResignationForm',
    label: '是否需上传离职手续表',
    componentProps: {
      placeholder: '请选择是否需上传离职手续表',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },
  {
    component: 'Select',
    fieldName: 'requireHandoverForm',
    label: '是否需离职交接单',
    componentProps: {
      placeholder: '请选择是否需离职交接单',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },

  // 第二行：是否需要离职申请模版、离职申请模版
  {
    component: 'Select',
    fieldName: 'requireResignationTemplate',
    label: '是否需要离职申请模版',
    componentProps: {
      placeholder: '请选择是否需要离职申请模版',
      allowClear: true,
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' },
      ],
    },
  },
  {
    component: 'Upload',
    fieldName: 'resignationTemplate',
    label: '离职申请模版',
    componentProps: {
      placeholder: '请上传离职申请模版',
      accept: '.doc,.docx,.pdf',
      maxCount: 1,
    },
  },
];

<script setup lang="ts">
import type { ProjectVo } from '#/api/bpo/project/model';

import { Page, useVbenModal } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { delProject, exportProject, getProjectList } from '#/api/bpo/project';

import { columns, querySchema } from './data';
import ProjectImportModalComponent from './project-import-modal.vue';
import ProjectModalComponent from './project-modal.vue';

defineOptions({ name: 'Project' });

const formOptions = {
  schema: querySchema(),
  showCollapseButton: true,
  actionButtonOptions: {
    showSubmitButton: true,
    showResetButton: true,
  },
};

const gridOptions = {
  id: 'ProjectTable',
  height: 'auto',
  showOverflow: 'tooltip' as const,
  keepSource: true,
  printConfig: {},
  exportConfig: {},
  checkboxConfig: {
    labelField: 'id',
    reserve: true,
    highlight: true,
    range: true,
  },
  pagerConfig: {
    enabled: true,
    pageSize: 20,
    pageSizes: [10, 20, 50, 100],
  },
  toolbarConfig: {
    refresh: true,
    import: false,
    export: false,
    print: false,
    zoom: false,
    custom: true,
  },
  proxyConfig: {
    seq: true,
    sort: true,
    filter: true,
    ajax: {
      query: async ({ page, sorts, filters, form }) => {
        console.log('VXE Table查询参数:', { page, sorts, filters, form });

        const params = {
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...form,
        };

        console.log('发送API请求参数:', params);

        try {
          const result = await getProjectList(params);
          console.log('API返回结果:', result);

          // 确保返回VXE Table需要的格式
          return {
            result: result.rows || [],
            page: {
              total: result.total || 0,
            },
          };
        } catch (error) {
          console.error('查询项目列表失败:', error);
          return {
            result: [],
            page: {
              total: 0,
            },
          };
        }
      },
    },
  },
  getPopupContainer: getVxePopupContainer,
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [ProjectModal, projectModalApi] = useVbenModal({
  connectedComponent: ProjectModalComponent,
});

const [ProjectImportModal, projectImportModalApi] = useVbenModal({
  connectedComponent: ProjectImportModalComponent,
});

async function handleAdd() {
  projectModalApi.setData({});
  projectModalApi.open();
}

async function handleEdit(row: ProjectVo) {
  projectModalApi.setData({ id: row.id });
  projectModalApi.open();
}

async function handleDelete(rows: ProjectVo[]) {
  if (!rows || rows.length === 0) {
    message.warning('请选择要删除的数据');
    return;
  }

  try {
    const ids = rows.map((row) => row.id).filter(Boolean) as (
      | number
      | string
    )[];
    await delProject(ids);
    message.success('删除成功');
    await tableApi.query();
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败');
  }
}

async function handleBatchDelete() {
  // 这里可以通过表格实例获取选中的行
  // 暂时提示用户使用行操作删除
  message.info('请使用行操作中的删除按钮进行删除');
}

async function handleExport() {
  try {
    // 获取当前查询参数
    const queryParams = {};
    await exportProject(queryParams);
    message.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  }
}

async function handleImport() {
  projectImportModalApi.open();
}
</script>

<template>
  <Page auto-content-height>
    <BasicTable
      :columns="columns"
      :form-options="formOptions"
      :grid-options="gridOptions"
      @register="tableApi"
    >
      <template #toolbar>
        <a-button
          v-auth="['bpo:project:add']"
          type="primary"
          @click="handleAdd"
        >
          <Icon icon="lucide:plus" class="mr-2 size-4" />
          新增
        </a-button>
        <a-button v-auth="['bpo:project:export']" @click="handleExport">
          <Icon icon="lucide:download" class="mr-2 size-4" />
          导出
        </a-button>
        <a-button v-auth="['bpo:project:import']" @click="handleImport">
          <Icon icon="lucide:upload" class="mr-2 size-4" />
          导入
        </a-button>
        <a-button v-auth="['bpo:project:remove']" @click="handleBatchDelete">
          <Icon icon="lucide:trash-2" class="mr-2 size-4" />
          删除
        </a-button>
      </template>

      <template #action="{ row }">
        <TableAction
          :actions="[
            {
              icon: 'lucide:square-pen',
              label: '编辑',
              auth: 'bpo:project:edit',
              onClick: handleEdit.bind(null, row),
            },
            {
              icon: 'lucide:trash-2',
              label: '删除',
              color: 'error',
              auth: 'bpo:project:remove',
              popConfirm: {
                title: '是否确认删除？',
                confirm: handleDelete.bind(null, [row]),
              },
            },
          ]"
        />
      </template>
    </BasicTable>

    <ProjectModal @reload="tableApi.query" />
    <ProjectImportModal @reload="tableApi.query()" />
  </Page>
</template>

<style scoped>
/* 自定义样式 */
:deep(.vxe-table--body-wrapper) {
  overflow-x: auto;
}

:deep(.vxe-table .vxe-cell) {
  padding: 8px 4px;
}

:deep(.vxe-table .vxe-header--column) {
  font-weight: 600;
  background-color: #fafafa;
}

:deep(.vxe-table .vxe-body--row:hover) {
  background-color: #f5f5f5;
}
</style>

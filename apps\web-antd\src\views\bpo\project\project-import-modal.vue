<script setup lang="ts">
import type { UploadFile } from 'ant-design-vue';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import {
  downloadProjectImportTemplate,
  importProject,
} from '#/api/bpo/project';

defineOptions({ name: 'ProjectImportModal' });

const emit = defineEmits(['reload']);

const loading = ref(false);
const fileList = ref<UploadFile[]>([]);
const updateSupport = ref(false);
const importResult = ref<null | { message: string; success: boolean }>(null);

const [ModalComponent, modalApi] = useVbenModal({
  onOpenChange: (isOpen: boolean) => {
    if (!isOpen) {
      // 重置状态
      fileList.value = [];
      updateSupport.value = false;
      importResult.value = null;
    }
  },
});

function beforeUpload(file: File) {
  const isExcel =
    file.type ===
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel';

  if (!isExcel) {
    message.error('只能上传Excel文件！');
    return false;
  }

  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB！');
    return false;
  }

  return false; // 阻止自动上传
}

function handleRemove() {
  fileList.value = [];
  importResult.value = null;
}

async function handleDownloadTemplate() {
  try {
    loading.value = true;
    const blob = await downloadProjectImportTemplate();

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = '项目导入模板.xlsx';
    document.body.append(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    message.success('模板下载成功');
  } catch (error) {
    console.error('下载模板失败:', error);
    message.error('下载模板失败');
  } finally {
    loading.value = false;
  }
}

async function handleSubmit() {
  if (fileList.value.length === 0) {
    message.warning('请选择要导入的文件');
    return;
  }

  try {
    loading.value = true;
    importResult.value = null;

    const formData = new FormData();
    formData.append('file', fileList.value[0]?.originFileObj as File);
    formData.append('updateSupport', updateSupport.value.toString());

    const result = await importProject(formData as any);

    if (result.code === 200) {
      importResult.value = {
        success: true,
        message: result.msg || '导入成功',
      };
      message.success('导入成功');
      emit('reload');

      // 延迟关闭弹窗，让用户看到结果
      setTimeout(() => {
        modalApi.close();
      }, 2000);
    } else {
      importResult.value = {
        success: false,
        message: result.msg || '导入失败',
      };
      message.error(result.msg || '导入失败');
    }
  } catch (error: any) {
    console.error('导入失败:', error);
    const errorMsg = error?.response?.data?.msg || error?.message || '导入失败';
    importResult.value = {
      success: false,
      message: errorMsg,
    };
    message.error(errorMsg);
  } finally {
    loading.value = false;
  }
}

function handleCancel() {
  modalApi.close();
}
</script>

<template>
  <ModalComponent
    title="项目导入"
    width="600px"
    :loading="loading"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <div class="p-6">
      <a-alert
        message="导入说明"
        description="请先下载导入模板，按照模板格式填写数据后上传。支持xlsx、xls格式文件。"
        type="info"
        show-icon
        class="mb-4"
      />

      <div class="mb-4">
        <a-button type="primary" @click="handleDownloadTemplate">
          <Icon icon="lucide:download" class="mr-2 size-4" />
          下载导入模板
        </a-button>
      </div>

      <a-form layout="vertical">
        <a-form-item label="选择文件" required>
          <a-upload
            v-model:file-list="fileList"
            :before-upload="beforeUpload"
            :remove="handleRemove"
            accept=".xlsx,.xls"
            :max-count="1"
          >
            <a-button>
              <Icon icon="lucide:upload" class="mr-2 size-4" />
              选择文件
            </a-button>
          </a-upload>
        </a-form-item>

        <a-form-item label="是否更新已存在数据">
          <a-switch v-model:checked="updateSupport" />
          <span class="ml-2 text-gray-500">开启后，会更新已存在的数据</span>
        </a-form-item>
      </a-form>

      <div v-if="importResult" class="mt-4">
        <a-alert
          :message="importResult.success ? '导入成功' : '导入失败'"
          :description="importResult.message"
          :type="importResult.success ? 'success' : 'error'"
          show-icon
        />
      </div>
    </div>
  </ModalComponent>
</template>

<style scoped>
:deep(.ant-upload-list) {
  margin-top: 8px;
}

:deep(.ant-alert) {
  border-radius: 6px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}
</style>

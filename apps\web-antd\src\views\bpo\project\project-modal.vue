<script setup lang="ts">
import type { CustomerVo } from '#/api/bpo/customer/model';
import type { ProjectBo } from '#/api/bpo/project/model';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getCustomerList } from '#/api/bpo/customer';
import { addProject, getProject, updateProject } from '#/api/bpo/project';
import { getDeptTree } from '#/api/system/user';

import { drawerSchema } from './drawer-data';

defineOptions({ name: 'ProjectModal' });

const emit = defineEmits(['reload']);

const isUpdate = ref(false);
const loading = ref(false);
const rowId = ref<number>();

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  schema: drawerSchema(),
  showDefaultActions: false,
  layout: 'horizontal',
  wrapperClass: 'grid grid-cols-2 gap-4',
});

const [ModalComponent, modalApi] = useVbenModal({
  onOpenChange: async (isOpen: boolean) => {
    if (!isOpen) return;

    // 先加载下拉选项数据
    await loadSelectOptions();

    const data = modalApi.getData();
    isUpdate.value = !!data?.id;
    rowId.value = data?.id;

    if (isUpdate.value && data?.id) {
      loading.value = true;
      try {
        const project = await getProject(data.id);
        await formApi.setValues({
          ...project,
          // 处理部门ID的类型转换
          departmentId: project.departmentId
            ? String(project.departmentId)
            : undefined,
        });
      } catch (error) {
        console.error('获取项目详情失败:', error);
        message.error('获取项目详情失败');
      } finally {
        loading.value = false;
      }
    } else {
      rowId.value = undefined;
      await formApi.resetForm();
    }
  },
});

const getTitle = computed(() => (isUpdate.value ? '编辑项目' : '新增项目'));

async function loadSelectOptions() {
  try {
    // 加载客户列表
    const customerResponse = await getCustomerList({
      pageNum: 1,
      pageSize: 1000,
    });
    const customerOptions =
      customerResponse.rows?.map((customer: CustomerVo) => ({
        label: customer.customerName,
        value: customer.id,
      })) || [];

    // 加载部门树
    const deptTree = await getDeptTree();

    // 更新表单schema中的选项
    formApi.updateSchema([
      {
        fieldName: 'customerId',
        componentProps: {
          options: customerOptions,
        },
      },
      {
        fieldName: 'departmentId',
        componentProps: {
          treeData: deptTree,
          fieldNames: {
            children: 'children',
            label: 'label',
            value: 'value',
          },
        },
      },
      // TODO: 这里需要加载用户列表用于项目经理和结算专员选择
      // 暂时使用空数组，后续需要调用用户API
      {
        fieldName: 'projectManagerId',
        componentProps: {
          options: [],
        },
      },
      {
        fieldName: 'settlementSpecialistId',
        componentProps: {
          options: [],
        },
      },
    ]);
  } catch (error) {
    console.error('加载选项数据失败:', error);
  }
}

async function handleSubmit() {
  try {
    const values = await formApi.validate();
    loading.value = true;

    const projectData: ProjectBo = {
      ...(values as any),
      // 处理部门ID的类型转换
      departmentId: (values as any).departmentId
        ? String((values as any).departmentId)
        : undefined,
    };

    if (isUpdate.value) {
      projectData.id = rowId.value;
      await updateProject(projectData);
      message.success('项目更新成功');
    } else {
      await addProject(projectData);
      message.success('项目创建成功');
    }

    modalApi.close();
    emit('reload');
  } catch (error) {
    console.error('保存项目失败:', error);
    message.error('保存项目失败');
  } finally {
    loading.value = false;
  }
}

async function handleCancel() {
  modalApi.close();
}
</script>

<template>
  <ModalComponent
    :title="getTitle"
    width="900px"
    :loading="loading"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <div class="p-6">
      <BasicForm />
    </div>
  </ModalComponent>
</template>

<style scoped>
/* 自定义样式，保持与图片相同的字段间距 */
:deep(.vben-form) {
  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-form-item-label {
    padding-bottom: 4px;
    font-weight: 500;
  }

  .ant-divider {
    margin: 24px 0 16px;
    font-size: 16px;
    font-weight: 600;
  }
}

/* 确保表单在900px宽度下的良好布局 */
:deep(.grid-cols-2) {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  align-items: start;
}

/* 分隔符和特定字段占满整行 */
:deep(.col-span-2) {
  grid-column: 1 / -1;
}

/* 表单项标签宽度 */
:deep(.ant-form-item-label) {
  min-width: 120px;
  padding-right: 8px;
  text-align: right;
}

/* 输入框样式 */
:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-picker),
:deep(.ant-input-number) {
  border-radius: 4px;
}

/* 文本域样式 */
:deep(.ant-input) {
  resize: none;
}
</style>

<script setup lang="ts">
import type { CustomerVo } from '#/api/bpo/customer/model';
import type { ProjectBo } from '#/api/bpo/project/model';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { addFullName, getPopupContainer } from '@vben/utils';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getCustomerList } from '#/api/bpo/customer';
import { addProject, getProject, updateProject } from '#/api/bpo/project';
import { getDeptTree } from '#/api/system/user';

import { drawerSchema } from './drawer-data';

defineOptions({ name: 'ProjectModal' });

const emit = defineEmits(['reload']);

const isUpdate = ref(false);
const loading = ref(false);
const rowId = ref<number>();

/**
 * 处理departmentId，转换为表单需要的格式
 * @param departmentId 部门ID，可能是字符串、数字或null/undefined
 * @returns 表单使用的部门ID或undefined
 */
function getDeptIdForForm(departmentId: any): number | undefined {
  console.log('处理departmentId:', {
    value: departmentId,
    type: typeof departmentId,
  });

  // 如果是null、undefined或空字符串，返回undefined
  if (!departmentId || departmentId === '') {
    console.log('departmentId为空，返回undefined');
    return undefined;
  }

  // 如果是数字类型
  if (typeof departmentId === 'number') {
    const result = departmentId > 0 ? departmentId : undefined;
    console.log('数字类型处理结果:', result);
    return result;
  }

  // 如果是字符串类型
  if (typeof departmentId === 'string') {
    const trimmed = departmentId.trim();
    if (trimmed === '' || trimmed === '0') {
      console.log('字符串为空或0，返回undefined');
      return undefined;
    }
    const num = Number(trimmed);
    const result = !isNaN(num) && num > 0 ? num : undefined;
    console.log('字符串类型处理结果:', { trimmed, num, result });
    return result;
  }

  // 其他类型，尝试转换为数字
  const num = Number(departmentId);
  const result = !isNaN(num) && num > 0 ? num : undefined;
  console.log('其他类型处理结果:', { num, result });
  return result;
}

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  schema: drawerSchema(),
  showDefaultActions: false,
  layout: 'horizontal',
  wrapperClass: 'grid grid-cols-2 gap-4',
});

const [ModalComponent, modalApi] = useVbenModal({
  onOpenChange: async (isOpen: boolean) => {
    if (!isOpen) return;

    // 先加载下拉选项数据
    await loadSelectOptions();

    const data = modalApi.getData();
    isUpdate.value = !!data?.id;
    rowId.value = data?.id;

    if (isUpdate.value && data?.id) {
      loading.value = true;
      try {
        const project = await getProject(data.id);
        console.log('获取到的项目数据:', project);
        console.log('departmentId详细信息:', {
          value: project.departmentId,
          type: typeof project.departmentId,
          isNull: project.departmentId === null,
          isUndefined: project.departmentId === undefined,
          isEmpty: project.departmentId === '',
        });

        // 构建表单数据，处理部门ID
        const formData = {
          ...project,
          // 使用getDeptIdForForm处理部门ID
          departmentId: getDeptIdForForm(project.departmentId),
        };
        console.log('设置表单数据:', formData);
        console.log('departmentId处理结果:', {
          原始值: project.departmentId,
          处理后: formData.departmentId,
          是否为undefined: formData.departmentId === undefined,
        });

        await formApi.setValues(formData);

        // 验证表单值是否正确设置
        const setValues = await formApi.getValues();
        console.log('设置后的表单值:', setValues);
      } catch (error) {
        console.error('获取项目详情失败:', error);
        message.error('获取项目详情失败');
      } finally {
        loading.value = false;
      }
    } else {
      rowId.value = undefined;
      await formApi.resetForm();
    }
  },
});

const getTitle = computed(() => (isUpdate.value ? '编辑项目' : '新增项目'));

/**
 * 初始化部门选择
 */
async function setupDeptSelect() {
  const deptTree = await getDeptTree();
  console.log('获取到的部门树数据:', deptTree);

  // 选中后显示在输入框的值 即父节点 / 子节点
  addFullName(deptTree, 'label', ' / ');

  formApi.updateSchema([
    {
      componentProps: {
        class: 'w-full',
        fieldNames: {
          key: 'id',
          value: 'id',
          children: 'children',
        },
        getPopupContainer,
        placeholder: '请选择部门',
        showSearch: true,
        treeData: deptTree,
        treeDefaultExpandAll: true,
        treeLine: { showLeafIcon: false },
        // 筛选的字段
        treeNodeFilterProp: 'label',
        // 选中后显示在输入框的值
        treeNodeLabelProp: 'fullName',
        // 确保没有默认值
        allowClear: true,
        value: undefined,
        defaultValue: undefined,
      },
      fieldName: 'departmentId',
      // 确保字段没有默认值
      defaultValue: undefined,
    },
  ]);
}

async function loadSelectOptions() {
  try {
    // 加载客户列表
    const customerResponse = await getCustomerList({
      pageNum: 1,
      pageSize: 1000,
    });
    const customerOptions =
      customerResponse.rows?.map((customer: CustomerVo) => ({
        label: customer.customerName,
        value: customer.id,
      })) || [];

    // 初始化部门选择（使用customer的方式）
    await setupDeptSelect();

    // 更新表单schema中的选项
    formApi.updateSchema([
      {
        fieldName: 'customerId',
        componentProps: {
          options: customerOptions,
        },
      },
      // TODO: 这里需要加载用户列表用于项目经理和结算专员选择
      // 暂时使用空数组，后续需要调用用户API
      {
        fieldName: 'projectManagerId',
        componentProps: {
          options: [],
        },
      },
      {
        fieldName: 'settlementSpecialistId',
        componentProps: {
          options: [],
        },
      },
    ]);
  } catch (error) {
    console.error('加载选项数据失败:', error);
  }
}

async function handleSubmit() {
  try {
    const values = await formApi.validate();
    loading.value = true;

    const projectData: ProjectBo = {
      ...(values as any),
      // 处理部门ID的类型转换
      departmentId: (values as any).departmentId
        ? String((values as any).departmentId)
        : undefined,
    };

    if (isUpdate.value) {
      projectData.id = rowId.value;
      await updateProject(projectData);
      message.success('项目更新成功');
    } else {
      await addProject(projectData);
      message.success('项目创建成功');
    }

    modalApi.close();
    emit('reload');
  } catch (error) {
    console.error('保存项目失败:', error);
    message.error('保存项目失败');
  } finally {
    loading.value = false;
  }
}

async function handleCancel() {
  modalApi.close();
}
</script>

<template>
  <ModalComponent
    :title="getTitle"
    width="900px"
    :loading="loading"
    :centered="true"
    :wrap-class-name="'project-modal-wrapper'"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <div class="project-modal-content">
      <BasicForm />
    </div>
  </ModalComponent>
</template>

<style scoped>
/* 弹窗居中样式 */
:global(.project-modal-wrapper) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:global(.project-modal-wrapper .ant-modal) {
  top: 0 !important;
  padding-bottom: 0 !important;
  margin: 0 auto !important;
  width: 900px !important;
  max-width: 900px !important;
  min-width: 900px !important;
}

/* 弹窗内容样式 */
.project-modal-content {
  padding: 24px;
  max-height: 80vh;
  overflow-y: auto;
}

/* 自定义样式，保持与图片相同的字段间距 */
:deep(.vben-form) {
  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-form-item-label {
    padding-bottom: 4px;
    font-weight: 500;
  }

  .ant-divider {
    margin: 24px 0 16px;
    font-size: 16px;
    font-weight: 600;
  }
}

/* 确保表单在900px宽度下的良好布局 */
:deep(.grid-cols-2) {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  align-items: start;
}

/* 分隔符和特定字段占满整行 */
:deep(.col-span-2) {
  grid-column: 1 / -1;
}

/* 表单项标签宽度 */
:deep(.ant-form-item-label) {
  min-width: 120px;
  padding-right: 8px;
  text-align: right;
}

/* 输入框样式 */
:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-picker),
:deep(.ant-input-number) {
  border-radius: 4px;
}

/* 文本域样式 */
:deep(.ant-input) {
  resize: none;
}

/* 部门选择器样式 */
:deep(.ant-select-tree) {
  .ant-select-tree-node-content-wrapper {
    padding: 2px 4px;
  }

  .ant-select-tree-title {
    font-size: 14px;
  }
}

/* 响应式设计 - 始终保持900px宽度 */
@media (max-width: 1000px) {
  :global(.project-modal-wrapper .ant-modal) {
    width: 900px !important;
    max-width: 900px !important;
    min-width: 900px !important;
  }
}

@media (max-height: 800px) {
  .project-modal-content {
    max-height: 70vh;
  }
}
</style>
